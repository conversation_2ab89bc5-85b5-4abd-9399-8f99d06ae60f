# Trigger.dev Integration

This document outlines the integration of Trigger.dev to replace PostgreSQL CRON jobs with a more robust background job processing system.

## Overview

We have successfully integrated Trigger.dev v3 to replace the existing PostgreSQL CRON jobs with scheduled tasks that provide:

- Better reliability and error handling
- Real-time monitoring and logging
- Automatic retries with exponential backoff
- Centralized job management through the Trigger.dev dashboard

## Architecture

### Package Structure

```
packages/jobs/
├── src/
│   ├── index.ts                           # Main exports
│   ├── tasks/                             # Task definitions
│   │   ├── cleanup-notifications.ts       # Notification cleanup task
│   │   └── cleanup-review-requests.ts     # Review request cleanup task
│   └── trigger/                           # Trigger.dev task exports
│       └── cleanup-jobs.ts                # Task exports for Trigger.dev
├── package.json                           # Package config with trigger scripts
├── tsconfig.json
└── README.md

trigger.config.ts                          # Trigger.dev configuration (root level)
```

All Trigger.dev related code and scripts are organized within the jobs package for better maintainability.

### Replaced CRON Jobs

| Original CRON Job | New Trigger.dev Task | Schedule | Description |
|-------------------|---------------------|----------|-------------|
| `delete-expired-notifications` | `cleanup-expired-notifications` | `0 0 * * 0` | Deletes expired and dismissed notifications |
| `delete-expired-review-requests` | `cleanup-expired-review-requests` | `0 0 * * 0` | Deletes expired review requests |

## Implementation Details

### Task Configuration

Both tasks are configured with:
- **Schedule**: Every Sunday at midnight UTC (`0 0 * * 0`)
- **Retry Policy**: 3 attempts with exponential backoff
- **Database Access**: Uses `dbAdmin` client to bypass RLS
- **Logging**: Structured logging with pino logger

### Database Operations

The tasks perform the same database operations as the original CRON jobs:

#### Notification Cleanup
```sql
DELETE FROM public.notification
WHERE expires_at < current_timestamp
   OR is_dismissed = true;
```

#### Review Request Cleanup
```sql
DELETE FROM user_review_request
WHERE expiration_date IS NOT NULL
  AND expiration_date < current_timestamp;
```

## Environment Variables

Add these environment variables to your `.env` file:

```bash
# Trigger.dev Configuration
TRIGGER_PROJECT_ID=your_trigger_project_id_here
TRIGGER_SECRET_KEY=your_trigger_secret_key_here
```

## Development

### Running Trigger.dev Development Server

```bash
# From the root directory
pnpm trigger:dev

# Or directly from the jobs package
pnpm --filter=@packages/jobs trigger:dev
```

This will:
- Start the Trigger.dev development server
- Watch for changes in the `packages/jobs/src/trigger` directory
- Register tasks with the Trigger.dev platform
- Enable local testing and debugging

### Testing Tasks

Tasks can be tested through the Trigger.dev dashboard:
1. Navigate to the Test page in the dashboard
2. Select the task you want to test
3. Configure any test parameters
4. Run the test and monitor the results

### Environment Setup

Ensure the following environment variables are set in your production environment:
- `TRIGGER_PROJECT_ID`
- `TRIGGER_SECRET_KEY`
- `DATABASE_URL`
- `ADMIN_DATABASE_URL`

## Monitoring

### Dashboard Access

Monitor job execution through the Trigger.dev dashboard at https://cloud.trigger.dev

### Logging

All tasks use structured logging with the following information:
- Task execution start/completion
- Number of records processed
- Error details with context
- Execution timestamps and schedule IDs

### Alerts

Configure alerts in the Trigger.dev dashboard for:
- Task failures
- Execution timeouts
- Retry exhaustion

## Migration Notes

### Removed Components

- PostgreSQL `pg_cron` extension usage
- CRON job definitions in `supabase/migrations/30000000000000_custom.sql`

### Added Components

- `@packages/jobs` package
- Trigger.dev SDK integration
- Scheduled task definitions
- Enhanced logging and monitoring

### Database Changes

The database schema remains unchanged. Only the execution mechanism has been replaced.

## Benefits

1. **Reliability**: Automatic retries and error handling
2. **Monitoring**: Real-time visibility into job execution
3. **Scalability**: Cloud-based execution with automatic scaling
4. **Maintainability**: Centralized job management and configuration
5. **Debugging**: Enhanced logging and error tracking
6. **Flexibility**: Easy to add new scheduled tasks or modify existing ones

## Next Steps

1. Set up Trigger.dev project and obtain API keys
2. Configure environment variables
3. Deploy tasks to production
4. Monitor initial execution
5. Set up alerts and notifications
6. Consider adding additional background jobs as needed
