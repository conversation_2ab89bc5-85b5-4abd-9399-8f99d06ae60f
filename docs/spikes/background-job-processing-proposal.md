# Background Job Processing System Proposal

This proposal provides a comprehensive solution for handling bulk notifications and background jobs while following the established patterns in the codebase. It leverages Inngest for reliable job processing and implements efficient batching strategies.

The system is designed to be scalable and maintainable, with clear separation of concerns and type safety throughout. It also provides a foundation for future features like job alerts and advanced engagement strategies.

## 2. System Components

### 2.1 Database Design

1. Send Batched Notifications:
- `get_users_with_unread_messages_count`: Returns users with unread messages count
- `get_inactive_users_with_inactivity_period`: Returns users with inactivity period

### 2.2 Inngest Functions

- `batch-connection-requests`: This function is triggered by the `connection.request` event. This event will be batched upto 50 events per batch and will run every 24 hours (Note: This can run before 24 hours if there are more than 50 events triggered). It counts the number of connection requests in the last 24 hours and sends a notification to the user.
- `unread-message-summary`: This function is scheduled to run every day. It counts the number of unread messages for a user and sends a notification to the user.
  - RPCs: `get_users_with_unread_messages_count`
- `reengagement-campaign`: This function is scheduled to run every week. It sends a reengagement campaign email to the user if the user has not logged in for a certain period of time.
  - RPCs: `get_inactive_users_with_inactivity_period`
- `job-alerts`: This function is scheduled to run every day. It sends a job alert to the user (Consultant) based on the user preferences (TBD).

### 2.3 - Frontend Components

- Email Templates:
  - Connection Requests Summary Email
  - Unread Messages Summary Email
  - Reengagement Campaign Email
  - Job Alerts Email

- In-App Notifications:
  - Connection Requests Summary
  - Unread Messages Summary
  - Job Alerts Notification

## Examples

### Batched Events - Batched Connection Requests Function

**How batching works**

When batching is enabled, Inngest creates a new batch when the first event is received. The batch is filled with events until the maxSize is reached or the timeout is up. The function is then invoked with the full list of events in the batch. When key is set, Inngest will maintain a batch for each unique key, which allows you to batch events belonging to a single entity, for example a user.

```typescript
// functions/connection-requests.ts
export const batchConnectionRequests = inngest.createFunction(
  { 
    id: 'batch-connection-requests',
    batchEvents: {
      maxSize: 50, // 50 events per batch
      timeout: "24h", // 24 hours to batch
      key: "event.data.receiverId", // Optional: batch events by user ID (That's an idempotency key)
    }, 
  },
  { event: 'connection.request' },
  async ({ event, step }) => {
    const { receiverId, requesterId } = event.data;
    
    // Get all requests in the last hour
    const { count } = await step.run('count-requests', async () => {
      const { count } = await supabase
        .from('connection_request')
        .select('*', { count: 'exact' })
        .eq('receiver_id', receiverId)
        .eq('is_read', false);
      
      return { count };
    });
    
    if (count > 30) {
      // Send email notification
      await step.run('send-email', async () => {
        await sendEmail({
          // ...
        });
      });
      
      // Send in-app notification
      await step.run('send-in-app-notification', async () => {
        await notificationApi.sendNotification({
          user_id: data.requesterId,
          type: NotificationType.BatchedConnectionRequests,
          content: `You have ${count}+ new connection requests`,
          metadata: { count },
        });
      });
    }
    
    return { processed: count };
  }
);
```

### Scheduled Events - Unread Messages Summary Function

```typescript
// Daily unread message summary
export const unreadMessageSummary = inngest.createScheduledFunction(
  { id: 'unread-message-summary' },
  { cron: '0 20 * * *' }, // 8pm daily
  async ({ step }) => {
    // Get users with unread messages
    const { usersWithUnreadMessages } = await step.run('get-users', async () => {
      const { data } = await supabase.rpc('get_users_with_unread_messages_count', {
        min_count: 5 // Only notify if at least 5 unread messages
      });
      
      return { usersWithUnreadMessages: data || [] };
    });
    
    // Send email to each user
    const { emails } = await step.run('send-emails', async () => {
      const emailsPromise = [];
      
      for (const user of usersWithUnreadMessages) {
        emailsPromise.push(sendEmail({
          // ...
        }));
      }
      
      const emails = await Promise.all(emailsPromise);
      return { emails };
    });

    // Send in-app notification
    await step.run('send-in-app-notification', async () => {
      const notificationsPromise = [];
      for (const user of usersWithUnreadMessages) {
        notificationsPromise.push(notificationApi.sendNotification({
          user_id: user.id,
          type: NotificationType.UnreadMessagesSummary,
          content: `You have ${user.unreadMessagesCount}+ new unread messages`,
          metadata: { count: user.unreadMessagesCount },
        }));
      }
      await Promise.all(notificationsPromise);
    });
    
    return { processed: emails.length };
  }
);
```

### Trigger Events from Server Actions

```typescript
// core/server/actions/connection-request/create.ts

export function createConnectionRequestAction(/* ,,, */) {
  // Send Connection Request
  // Then trigger event for background processing
  await inngest.send({
    name: 'connection.request',
    data: {
      requesterId: authUser.id,
      receiverId: receiverId
    },
  });
}
```

### Workflow Examples

#### 1. Batched Connection Requests

<img width="600" alt="batched connection requests" src="./_assets/batched-connection-requests.jpg" />

#### 2. Unread Messages Summary

<img width="600" alt="unread messages summary" src="./_assets/unread-messages-summary.jpg" />

#### 3. Re-engagement Campaign

<img width="600" alt="re-engagement campaign" src="./_assets/re-engagement-campaign.jpg" />

#### 4. Job Alerts

<img width="600" alt="job alerts" src="./_assets/job-alerts.jpg" />