# Migration Guide: PostgreSQL CRON to Trigger.dev

This guide walks through the steps to migrate from PostgreSQL CRON jobs to Trigger.dev scheduled tasks.

## Prerequisites

1. **Trigger.dev Account**: Create an account at https://cloud.trigger.dev
2. **Project Setup**: Create a new project in your Trigger.dev dashboard
3. **API Keys**: Obtain your project ID and secret key

## Step 1: Install Dependencies

The integration has already been set up. To install dependencies:

```bash
pnpm install
```

## Step 2: Environment Configuration

Add the following environment variables to your `web/.env.local` and `packages/jobs/.env.local` files:

```bash
# Trigger.dev Configuration
TRIGGER_PROJECT_ID=your_project_id_from_dashboard
TRIGGER_SECRET_KEY=your_secret_key_from_dashboard

# Database URLs
DATABASE_URL=your_database_url
ADMIN_DATABASE_URL=your_admin_database_url
# Note: You need these environment variables in the jobs package as well, in order to let the trigger dev server connect to the database while running scheduled tasks.
```

## Step 3: Database Migration

The CRON jobs have been removed from the database migration file. To apply this change:

```bash
# Reset the database to apply the updated migration
pnpm supareset
```

## Step 4: Development Setup

Start the Trigger.dev development server:

```bash
pnpm trigger:dev
```

This will:
- Connect to your Trigger.dev project
- Register the cleanup tasks
- Enable local development and testing

## Step 5: Test the Tasks

1. Open the Trigger.dev dashboard
2. Navigate to the "Test" section
3. Find your cleanup tasks:
   - `cleanup-expired-notifications`
   - `cleanup-expired-review-requests`
4. Run test executions to verify functionality

## Step 6: Verify Migration

### Check Task Registration

In the Trigger.dev dashboard:
1. Go to "Tasks" section
2. Verify both cleanup tasks are listed
3. Check their schedules are set to `0 0 * * 0` (weekly)

### Monitor First Execution

1. Wait for the next scheduled execution (Sunday midnight UTC)
2. Monitor the execution in the dashboard
3. Check logs for successful completion
4. Verify database records are being cleaned up

## Step 7: Set Up Monitoring

### Configure Alerts

In the Trigger.dev dashboard:
1. Go to "Alerts" section
2. Set up alerts for:
   - Task failures
   - Execution timeouts
   - Retry exhaustion

### Log Monitoring

Monitor structured logs for:
- Task execution start/completion
- Number of records processed
- Any errors or warnings

## Rollback Plan

If issues arise, you can temporarily rollback by:

1. **Re-enable CRON jobs** (if needed):
   ```sql
   CREATE EXTENSION IF NOT EXISTS pg_cron;
   
   SELECT cron.schedule('delete-expired-notifications', '0 0 * * 0', $$
     DELETE FROM public.notification
     WHERE expires_at < current_timestamp
         OR is_dismissed = true;
   $$);
   
   SELECT cron.schedule('delete-expired-review-requests', '0 0 * * 0', $$
     DELETE FROM user_review_request
     WHERE expiration_date IS NOT NULL
       AND expiration_date < current_timestamp;
   $$);
   ```

2. **Disable Trigger.dev tasks** in the dashboard

### Getting Help

- **Trigger.dev Documentation**: https://trigger.dev/docs

## Benefits Achieved

After successful migration:

✅ **Improved Reliability**: Automatic retries and error handling  
✅ **Better Monitoring**: Real-time visibility into job execution  
✅ **Enhanced Logging**: Structured logs with context  
✅ **Centralized Management**: Single dashboard for all background jobs  
✅ **Scalability**: Cloud-based execution with automatic scaling  
✅ **Maintainability**: Easier to add, modify, and debug tasks  

## Next Steps

1. **Monitor Performance**: Track execution times and success rates
2. **Add More Tasks**: Consider migrating other background processes
3. **Optimize Schedules**: Adjust timing based on usage patterns
4. **Set Up Notifications**: Configure team alerts for critical failures
5. **Documentation**: Update team documentation with new processes
