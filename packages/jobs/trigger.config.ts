import { defineConfig } from '@trigger.dev/sdk/v3';
import dotenv from 'dotenv';

dotenv.config({ path: `${process.cwd()}/.env` });

export default defineConfig({
  project: process.env.TRIGGER_PROJECT_ID!,
  runtime: 'node',
  logLevel: 'log',
  maxDuration: 60,
  retries: {
    enabledInDev: true,
    default: {
      maxAttempts: 3,
      minTimeoutInMs: 1000,
      maxTimeoutInMs: 10000,
      factor: 2,
      randomize: true,
    },
  },
  dirs: ['./src/tasks'],
});
