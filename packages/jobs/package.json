{"name": "@packages/jobs", "version": "0.0.0", "private": true, "main": "./src/index.ts", "scripts": {"clean": "rm -rf .turbo node_modules .trigger", "lint": "biome lint", "format": "biome format --write .", "typecheck": "tsc --noEmit", "dev": "npx trigger.dev@latest dev --config ./trigger.config.ts | pino-pretty"}, "dependencies": {"@packages/database": "workspace:*", "@packages/utils": "workspace:*", "@trigger.dev/sdk": "^3.3.17"}, "devDependencies": {"@tooling/typescript": "workspace:*", "dotenv": "^16.5.0", "pino-pretty": "^11.0.0", "typescript": "^5"}, "exports": {".": "./src/index.ts"}}