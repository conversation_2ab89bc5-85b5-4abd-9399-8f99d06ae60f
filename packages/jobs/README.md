# Jobs Package

This package contains background job definitions using Trigger.dev for scheduled tasks and background processing.

## Overview

This package replaces the previous PostgreSQL CRON jobs with Trigger.dev scheduled tasks, providing better reliability, monitoring, and error handling.

## Package Structure

```
packages/jobs/
├── src/
│   ├── index.ts                           # Main exports
│   ├── tasks/                             # Task definitions
│   │   ├── cleanup-notifications.ts        # Notification cleanup task
│   │   └── cleanup-review-requests.ts     # Review request cleanup task
│   └── └── etc...                         # More tasks...
├── trigger.config.ts                       # Trigger.dev configuration
├── package.json                           # Package configuration with trigger scripts
├── tsconfig.json                           # TypeScript configuration
└── README.md                              # This file
```

All Trigger.dev related code, configuration, and scripts are contained within this package for better organization.

## Tasks

### Cleanup Tasks

#### `cleanupExpiredNotifications`
- **Schedule**: Every Sunday at midnight UTC (`0 0 * * 0`)
- **Purpose**: Removes expired notifications and dismissed notifications from the database
- **Replaces**: `delete-expired-notifications` CRON job

#### `cleanupExpiredReviewRequests`
- **Schedule**: Every Sunday at midnight UTC (`0 0 * * 0`)
- **Purpose**: Removes expired review requests from the database
- **Replaces**: `delete-expired-review-requests` CRON job

## Environment Variables

The following environment variables are required:

- `TRIGGER_PROJECT_ID`: Your Trigger.dev project ID
- `TRIGGER_SECRET_KEY`: Your Trigger.dev secret key (for production)

## Development

To run the trigger.dev development server:

```bash
# From the root directory
pnpm trigger:dev

# Or from the jobs package directory
pnpm --filter=@packages/jobs dev
```

## Monitoring

Jobs can be monitored through the Trigger.dev dashboard at https://cloud.trigger.dev
