import { dbAdmin } from '@packages/database';
import { and, isNotNull, lt } from '@packages/database/drizzle-orm';
import { userReviewRequestTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';
import { schedules } from '@trigger.dev/sdk/v3';

export const cleanupExpiredReviewRequests = schedules.task({
  id: 'cleanup-expired-review-requests',
  // Run every Sunday at midnight UTC (same as original CRON: '0 0 * * 0')
  cron: '0 0 * * 0',
  run: async (payload) => {
    logger.info(
      { timestamp: payload.timestamp },
      '🧹 STARTING CLEANUP OF EXPIRED REVIEW REQUESTS'
    );

    try {
      const result = await dbAdmin
        .delete(userReviewRequestTable)
        .where(
          and(
            isNotNull(userReviewRequestTable.expirationDate),
            lt(userReviewRequestTable.expirationDate, new Date())
          )
        )
        .returning({ id: userReviewRequestTable.id });

      const deletedCount = result.length;

      logger.info(
        { deletedCount },
        '✅ SUCCESSFULLY DELETED EXPIRED REVIEW REQUESTS'
      );

      return {
        success: true,
        deletedCount,
        timestamp: payload.timestamp,
        scheduleId: payload.scheduleId,
      };
    } catch (error) {
      logger.error({ error }, '❌ FAILED TO CLEANUP EXPIRED REVIEW REQUESTS');
      throw error;
    }
  },
});
