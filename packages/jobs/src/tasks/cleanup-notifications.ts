import { schedules } from '@trigger.dev/sdk/v3';

import { dbAdmin } from '@packages/database';
import { eq, lt, or } from '@packages/database/drizzle-orm';
import { notificationTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

export const cleanupExpiredNotifications = schedules.task({
  id: 'cleanup-expired-notifications',
  // Run every Sunday at midnight UTC (same as original CRON: '0 0 * * 0')
  cron: '0 0 * * 0',
  run: async (payload) => {
    logger.info(
      { timestamp: payload.timestamp },
      '🧹 STARTING CLEANUP OF EXPIRED NOTIFICATIONS'
    );

    try {
      const result = await dbAdmin
        .delete(notificationTable)
        .where(
          or(
            lt(notificationTable.expiresAt, new Date()),
            eq(notificationTable.isDismissed, true)
          )
        )
        .returning({ id: notificationTable.id });

      const deletedCount = result.length;

      logger.info(
        { deletedCount },
        '✅ SUCCESSFULLY DELETED EXPIRED/DISMISSED NOTIFICATIONS'
      );

      return {
        success: true,
        deletedCount,
        timestamp: payload.timestamp,
        scheduleId: payload.scheduleId,
      };
    } catch (error) {
      logger.error({ error }, '❌ FAILED TO CLEANUP EXPIRED NOTIFICATIONS');
      throw error;
    }
  },
});
