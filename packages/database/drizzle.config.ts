import dotenv from 'dotenv';
import { defineConfig } from 'drizzle-kit';

dotenv.config({ path: `${process.cwd()}/.env` });

const url = process.env.ADMIN_DATABASE_URL;

if (!url) {
  throw new Error('ADMIN_DATABASE_URL is not set');
}

export default defineConfig({
  dialect: 'postgresql',
  dbCredentials: {
    url,
  },
  schema: './src/schema/index.ts',
  out: '../../supabase/migrations',
  strict: true,
  verbose: false,
  schemaFilter: ['public'],
  casing: 'snake_case',
  migrations: {
    prefix: 'timestamp',
  },
  entities: {
    roles: {
      provider: 'supabase',
    },
  },
});
