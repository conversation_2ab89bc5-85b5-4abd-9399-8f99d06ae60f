import { MembershipRole } from '@packages/database/enums';
import type { DrizzleSupabaseClient } from '../index';
import type * as DTO from '../schema/types';

import { and, eq, inArray, or, sql } from 'drizzle-orm';

import { logger } from '@packages/utils/logger';
import { authUsersTable } from '../schema/auth';
import {
  addressTable,
  agencyContractTable,
  agencyInsightTable,
  certificationTable,
  chatRoomMemberTable,
  chatRoomTable,
  connectionRequestTable,
  connectionTable,
  departmentContractTable,
  educationTable,
  experiencePositionTable,
  experienceSkillTable,
  experienceTable,
  feedbackFileTable,
  feedbackTable,
  fullTimeAvailabilityTable,
  governmentContractTable,
  invitationTable,
  majorTable,
  membershipTable,
  messageTable,
  minorTable,
  officeContractTable,
  organizationAddressTable,
  organizationJoinRequestTable,
  organizationTable,
  pastPerformanceTable,
  socialLinkTable,
  subOfficeContractTable,
  userAddressTable,
  userCertificationTable,
  userDegreeTable,
  userPastPerformanceTable,
  userReviewRequestTable,
  userReviewTable,
  userSkillTable,
  usersTable,
} from '../schema/tables';
import { getChatRoomIdForUsers } from './queries';
import { getOrCreatePosition } from './utils';

type WithUserId<T> = T & { userId: string };

export async function createOrganization(
  db: DrizzleSupabaseClient,
  params: WithUserId<DTO.OrganizationRequest & Partial<DTO.AddressRequest>>
) {
  const {
    userId,
    name,
    description,
    ein,
    cageCode,
    primaryNaicsCode,
    address1,
    address2,
    state,
    city,
    zipCode,
  } = params;

  return await db.transaction(async (tx) => {
    const [newOrg] = await tx
      .insert(organizationTable)
      .values({ name, description, ein, cageCode, primaryNaicsCode })
      .returning({ organizationId: organizationTable.id });

    const { organizationId } = newOrg ?? {};

    if (!organizationId) {
      throw new Error('❌ FAILED TO CREATE ORGANIZATION');
    }

    if (address1 && state && city) {
      const [newAddress] = await tx
        .insert(addressTable)
        .values({ address1, address2, city, state, zipCode })
        .returning({ addressId: addressTable.id });

      const { addressId } = newAddress ?? {};

      if (!addressId) {
        throw new Error('❌ FAILED TO CREATE ADDRESS');
      }

      await tx.insert(organizationAddressTable).values({
        addressId,
        organizationId,
      });
    }

    await tx
      .insert(membershipTable)
      .values({ userId, organizationId, roleId: MembershipRole.Owner });

    return organizationId;
  });
}

export async function updateOrganization(
  db: DrizzleSupabaseClient,
  params: DTO.OrganizationRequest &
    Partial<DTO.AddressRequest> & {
      organizationId: string;
    }
) {
  const {
    organizationId,
    name,
    description = '',
    logo = '',
    ein,
    cageCode,
    primaryNaicsCode,
    address1,
    address2,
    state,
    city,
    zipCode,
  } = params;

  return await db.transaction(async (tx) => {
    await tx
      .update(organizationTable)
      .set({ logo, name, description, ein, cageCode, primaryNaicsCode })
      .where(eq(organizationTable.id, organizationId));

    const [orgAddress] = await tx
      .select({ addressId: organizationAddressTable.addressId })
      .from(organizationAddressTable)
      .where(eq(organizationAddressTable.organizationId, organizationId));

    const { addressId } = orgAddress ?? {};

    if (!addressId) {
      if (address1 && state && city) {
        const [newAddress] = await tx
          .insert(addressTable)
          .values({ address1, address2, city, state, zipCode })
          .returning({ newAddressId: addressTable.id });

        const { newAddressId } = newAddress ?? {};

        if (!newAddressId) {
          throw new Error('❌ FAILED TO CREATE ADDRESS');
        }

        await tx.insert(organizationAddressTable).values({
          addressId: newAddressId,
          organizationId,
        });
      }
      return organizationId;
    }

    if (address1 && state && city) {
      await tx
        .update(addressTable)
        .set({ address1, address2, city, state, zipCode })
        .where(eq(addressTable.id, addressId));
    } else {
      await tx.delete(addressTable).where(eq(addressTable.id, addressId));
    }

    return organizationId;
  });
}

export async function inviteMembersToOrganization(
  db: DrizzleSupabaseClient,
  params: {
    inviterId: string;
    members: Omit<DTO.InvitationRequest, 'status'>[];
  }
) {
  const { inviterId, members } = params;

  return await db.transaction(async (tx) => {
    const [membership] = await tx
      .select({
        roleId: membershipTable.roleId,
        organization: {
          id: organizationTable.id,
          logo: organizationTable.logo,
          name: organizationTable.name,
        },
        user: {
          id: usersTable.id,
          firstName: usersTable.firstName,
          lastName: usersTable.lastName,
        },
      })
      .from(membershipTable)
      .innerJoin(usersTable, eq(membershipTable.userId, usersTable.id))
      .innerJoin(
        organizationTable,
        eq(membershipTable.organizationId, organizationTable.id)
      )
      .where(eq(membershipTable.userId, inviterId));

    const { roleId, organization, user } = membership ?? {};

    if (roleId === undefined || !organization || !user) {
      throw new Error('❌ FAILED TO RETRIEVE MEMBERSHIP');
    }

    if (roleId < MembershipRole.Admin) {
      throw new Error('❌ USER DOES NOT HAVE PERMISSION TO INVITE MEMBERS');
    }

    const organizationId = organization.id;

    const emails: string[] = [];

    for (const member of members) {
      const { email, roleId, code, firstName, lastName } = member;

      if (roleId > MembershipRole.Admin) {
        continue;
      }

      const invitationId = await tx
        .select({ id: invitationTable.id })
        .from(invitationTable)
        .where(
          and(
            eq(invitationTable.email, email),
            eq(invitationTable.status, 'PENDING'),
            eq(invitationTable.organizationId, organizationId)
          )
        )
        .then((result) => result[0]?.id);

      if (invitationId) {
        await tx
          .update(invitationTable)
          .set({ roleId, code, firstName, lastName })
          .where(eq(invitationTable.id, invitationId));
      } else {
        await tx.insert(invitationTable).values({
          email,
          roleId,
          code,
          firstName,
          lastName,
          organizationId,
          inviterId,
          status: 'PENDING',
        });
      }

      emails.push(email);
    }

    return { emails, organization, user };
  });
}

export async function acceptInviteToOrganization(
  db: DrizzleSupabaseClient,
  params: {
    inviteCode: string;
    invitedUserId: string;
    invitedUserEmail: string;
    invitedUserFirstName?: string;
    invitedUserLastName?: string;
  }
) {
  const {
    inviteCode,
    invitedUserId,
    invitedUserEmail,
    invitedUserFirstName,
    invitedUserLastName,
  } = params;

  return await db.transaction(async (tx) => {
    const [invitation] = await tx
      .update(invitationTable)
      .set({ status: 'ACCEPTED', code: null })
      .where(
        and(
          eq(invitationTable.code, inviteCode),
          eq(invitationTable.email, invitedUserEmail),
          eq(invitationTable.status, 'PENDING')
        )
      )
      .returning({
        roleId: invitationTable.roleId,
        organizationId: invitationTable.organizationId,
      });

    const { organizationId, roleId } = invitation ?? {};

    if (!organizationId || roleId === undefined) {
      throw new Error('❌ FAILED TO ACCEPT INVITE TO ORGANIZATION');
    }

    const userHasMembership = await tx
      .select({ exists: sql<number>`1` })
      .from(membershipTable)
      .where(eq(membershipTable.userId, invitedUserId))
      .then((result) => result.length > 0);

    if (userHasMembership) {
      const [updatedMembership] = await tx
        .update(membershipTable)
        .set({ organizationId, roleId })
        .where(eq(membershipTable.userId, invitedUserId))
        .returning({ membershipId: membershipTable.id });

      const { membershipId } = updatedMembership ?? {};

      if (!membershipId) {
        throw new Error('❌ FAILED TO UPDATE MEMBERSHIP');
      }

      return {
        organizationId,
        membershipId,
      };
    }

    const userExists = await tx
      .select({ id: usersTable.id })
      .from(usersTable)
      .where(eq(usersTable.id, invitedUserId))
      .then((result) => result.length > 0);

    if (!userExists) {
      await tx.insert(usersTable).values({
        id: invitedUserId,
        firstName: invitedUserFirstName ?? '',
        lastName: invitedUserLastName ?? '',
      });
    }

    if (userExists && (!!invitedUserFirstName || !!invitedUserLastName)) {
      await tx
        .update(usersTable)
        .set({
          firstName: invitedUserFirstName,
          lastName: invitedUserLastName,
        })
        .where(
          and(
            eq(usersTable.id, invitedUserId),
            sql`${usersTable.firstName} is null`,
            sql`${usersTable.lastName} is null`
          )
        );
    }

    const [newMembership] = await tx
      .insert(membershipTable)
      .values({
        userId: invitedUserId,
        organizationId,
        roleId,
      })
      .returning({ membershipId: membershipTable.id });

    const { membershipId } = newMembership ?? {};

    if (!membershipId) {
      throw new Error('❌ FAILED TO CREATE MEMBERSHIP');
    }

    return {
      organizationId,
      membershipId,
    };
  });
}

export async function transferOwnership(
  db: DrizzleSupabaseClient,
  params: {
    organizationId: string;
    targetUserMembershipId: number;
  }
) {
  const { organizationId, targetUserMembershipId } = params;

  await db.transaction(async (tx) => {
    const [currentUserMembershipResult] = await tx
      .select({
        id: membershipTable.id,
        organizationId: membershipTable.organizationId,
        roleId: membershipTable.roleId,
      })
      .from(membershipTable)
      .where(eq(membershipTable.userId, sql`auth.uid()`));

    const {
      id: currentUserMembershipId,
      organizationId: currentUserOrganizationId,
      roleId: currentUserRoleId,
    } = currentUserMembershipResult ?? {};

    if (
      !currentUserMembershipId ||
      !currentUserOrganizationId ||
      currentUserRoleId === undefined
    ) {
      throw new Error('❌ FAILED TO GET CURRENT USER MEMBERSHIP');
    }

    if (currentUserOrganizationId !== organizationId) {
      throw new Error('❌ CURRENT USER IS NOT A MEMBER OF THE ORGANIZATION');
    }

    if (currentUserRoleId !== MembershipRole.Owner) {
      throw new Error('❌ ONLY OWNERS CAN TRANSFER ORGANIZATIONS');
    }

    if (currentUserMembershipId === targetUserMembershipId) {
      throw new Error('❌ CANNOT TRANSFER ORGANIZATION TO YOURSELF');
    }

    const [targetMembership] = await tx
      .update(membershipTable)
      .set({ roleId: MembershipRole.Owner })
      .where(eq(membershipTable.id, targetUserMembershipId))
      .returning({ id: membershipTable.id });

    if (!targetMembership?.id) {
      throw new Error('❌ FAILED TO UPDATE TARGET USER MEMBERSHIP');
    }

    const [currentMembership] = await tx
      .update(membershipTable)
      .set({ roleId: MembershipRole.Admin })
      .where(
        and(
          eq(membershipTable.userId, sql`auth.uid()`),
          eq(membershipTable.organizationId, organizationId)
        )
      )
      .returning({ id: membershipTable.id });

    if (!currentMembership?.id) {
      throw new Error('❌ FAILED TO UPDATE CURRENT USER MEMBERSHIP');
    }
  });
}

export async function updateUserAndAddress(
  db: DrizzleSupabaseClient,
  params: WithUserId<
    DTO.UsersRequest & {
      city: string;
      state: string;
    }
  >
) {
  const {
    userId,
    avatar,
    firstName,
    lastName,
    username,
    phoneNumber,
    headline,
    biography,
    payRateId,
    securityClearance,
    legalStatus,
    city,
    state,
  } = params;

  return await db.transaction(async (tx) => {
    await tx
      .update(usersTable)
      .set({
        firstName,
        lastName,
        avatar,
        username,
        phoneNumber,
        headline,
        biography,
        payRateId,
        securityClearance,
        legalStatus,
      })
      .where(eq(usersTable.id, userId));

    const [userAddress] = await tx
      .select({ addressId: userAddressTable.addressId })
      .from(userAddressTable)
      .where(eq(userAddressTable.userId, userId));

    const { addressId } = userAddress ?? {};

    if (!addressId) {
      const [newAddress] = await tx
        .insert(addressTable)
        .values({ city, state })
        .returning({ newAddressId: addressTable.id });

      const { newAddressId } = newAddress ?? {};

      if (!newAddressId) {
        throw new Error('❌ FAILED TO CREATE ADDRESS');
      }

      await tx.insert(userAddressTable).values({
        addressId: newAddressId,
        userId,
      });

      return newAddressId;
    }

    await tx
      .update(addressTable)
      .set({ city, state })
      .where(eq(addressTable.id, addressId));

    return addressId;
  });
}

export async function createWorkExperience(
  db: DrizzleSupabaseClient,
  params: DTO.ExperienceRequest & {
    positionName: string;
    positionId?: number;
    skillIds?: number[];
  }
) {
  const {
    userId,
    companyName,
    locationType,
    employmentType,
    isCurrentRole,
    startDate,
    city,
    state,
    endDate,
    description,
    positionName,
    skillIds = [],
  } = params;

  let { positionId } = params;

  return await db.transaction(async (tx) => {
    const [newExperience] = await tx
      .insert(experienceTable)
      .values({
        userId,
        companyName,
        locationType,
        employmentType,
        isCurrentRole,
        description,
        startDate,
        endDate,
        city,
        state,
      })
      .returning({ experienceId: experienceTable.id });

    const { experienceId } = newExperience ?? {};

    if (!experienceId) {
      throw new Error('❌ FAILED TO CREATE EXPERIENCE');
    }

    if (!positionId) {
      positionId = await getOrCreatePosition(tx, positionName);
    }

    if (!positionId) {
      throw new Error('❌ FAILED TO CREATE POSITION');
    }

    await tx
      .insert(experiencePositionTable)
      .values({ experienceId, positionId });

    if (skillIds.length > 0) {
      for (const skillId of skillIds) {
        await tx.insert(experienceSkillTable).values({ skillId, experienceId });
      }
    }

    return experienceId;
  });
}

export async function updateWorkExperience(
  db: DrizzleSupabaseClient,
  params: DTO.ExperienceRequest & {
    experienceId: number;
    city: string;
    state: string;
    positionName: string;
    positionId?: number;
    skillIds?: number[];
  }
) {
  const {
    experienceId,
    companyName,
    locationType,
    employmentType,
    startDate,
    city,
    state,
    positionName,
    endDate,
    description,
    isCurrentRole = false,
    skillIds = [],
  } = params;

  let { positionId } = params;

  return await db.transaction(async (tx) => {
    await tx
      .update(experienceTable)
      .set({
        companyName,
        locationType,
        employmentType,
        isCurrentRole,
        startDate,
        endDate,
        city,
        state,
        description,
      })
      .where(eq(experienceTable.id, experienceId));

    if (!positionId) {
      positionId = await getOrCreatePosition(tx, positionName);
    }

    if (!positionId) {
      throw new Error('❌ FAILED TO CREATE POSITION');
    }

    await tx
      .update(experiencePositionTable)
      .set({ positionId })
      .where(eq(experiencePositionTable.experienceId, experienceId));

    if (skillIds.length > 0) {
      const currentSkillIds = await tx
        .select({ skillId: experienceSkillTable.skillId })
        .from(experienceSkillTable)
        .where(eq(experienceSkillTable.experienceId, experienceId))
        .then((result) => result.map((row) => row.skillId));

      const skillsToAdd = skillIds.filter(
        (id) => !currentSkillIds.includes(id)
      );
      const skillsToRemove = currentSkillIds.filter(
        (id) => !skillIds.includes(id)
      );

      if (skillsToRemove.length > 0) {
        await tx
          .delete(experienceSkillTable)
          .where(
            and(
              eq(experienceSkillTable.experienceId, experienceId),
              inArray(experienceSkillTable.skillId, skillsToRemove)
            )
          );
      }

      if (skillsToAdd.length > 0) {
        await tx
          .insert(experienceSkillTable)
          .values(skillsToAdd.map((skillId) => ({ skillId, experienceId })));
      }
    } else {
      await tx
        .delete(experienceSkillTable)
        .where(eq(experienceSkillTable.experienceId, experienceId));
    }

    return experienceId;
  });
}

type PastPerformanceParams = {
  contractName: string;
  contractValue: number;
  contractDescription: string;
  serviceDescription: string;
  contractRfpNumber: string;
  awardDate: string;
  positionName: string;
  positionId?: number;
  departmentId?: number;
  agencyId?: number;
  officeId?: number;
  subOfficeId?: number;
};

export async function createPastPerformance(
  db: DrizzleSupabaseClient,
  params: WithUserId<PastPerformanceParams>
) {
  const {
    userId,
    contractName,
    contractValue,
    contractDescription,
    contractRfpNumber,
    serviceDescription,
    awardDate,
    departmentId,
    agencyId,
    officeId,
    subOfficeId,
    positionName,
  } = params;

  let { positionId } = params;

  return await db.transaction(async (tx) => {
    const [newContract] = await tx
      .insert(governmentContractTable)
      .values({
        name: contractName,
        value: contractValue.toString(),
        description: contractDescription,
        rfpNumber: contractRfpNumber,
      })
      .returning({ governmentContractId: governmentContractTable.id });

    const { governmentContractId } = newContract ?? {};

    if (!governmentContractId) {
      throw new Error('❌ FAILED TO CREATE GOVERNMENT CONTRACT');
    }

    if (!positionId) {
      positionId = await getOrCreatePosition(tx, positionName);
    }

    if (!positionId) {
      throw new Error('❌ FAILED TO CREATE POSITION');
    }

    const [newPastPerformance] = await tx
      .insert(pastPerformanceTable)
      .values({
        awardDate,
        positionId,
        governmentContractId,
        description: serviceDescription,
      })
      .returning({ pastPerformanceId: pastPerformanceTable.id });

    const { pastPerformanceId } = newPastPerformance ?? {};

    if (!pastPerformanceId) {
      throw new Error('❌ FAILED TO CREATE PAST PERFORMANCE');
    }

    await tx
      .insert(userPastPerformanceTable)
      .values({ userId, pastPerformanceId });

    if (departmentId) {
      await tx
        .insert(departmentContractTable)
        .values({ departmentId, governmentContractId });
    }

    if (agencyId) {
      await tx
        .insert(agencyContractTable)
        .values({ agencyId, governmentContractId });
    }

    if (officeId) {
      await tx
        .insert(officeContractTable)
        .values({ officeId, governmentContractId });
    }

    if (subOfficeId) {
      await tx
        .insert(subOfficeContractTable)
        .values({ subOfficeId, governmentContractId });
    }

    return { pastPerformanceId, governmentContractId };
  });
}

export async function updatePastPerformance(
  db: DrizzleSupabaseClient,
  params: PastPerformanceParams & {
    pastPerformanceId: number;
  }
) {
  const {
    pastPerformanceId,
    positionId,
    serviceDescription,
    contractName,
    contractValue,
    contractDescription,
    contractRfpNumber,
    awardDate,
    departmentId,
    agencyId,
    officeId,
    subOfficeId,
  } = params;

  return await db.transaction(async (tx) => {
    const pastPerformanceResult = await tx
      .update(pastPerformanceTable)
      .set({
        awardDate,
        positionId,
        description: serviceDescription,
      })
      .where(eq(pastPerformanceTable.id, pastPerformanceId))
      .returning({
        governmentContractId: pastPerformanceTable.governmentContractId,
      });

    const { governmentContractId } = pastPerformanceResult[0]!;

    await tx
      .update(governmentContractTable)
      .set({
        name: contractName,
        value: contractValue.toString(),
        description: contractDescription,
        rfpNumber: contractRfpNumber,
      })
      .where(eq(governmentContractTable.id, governmentContractId));

    if (departmentId) {
      await tx
        .insert(departmentContractTable)
        .values({ departmentId, governmentContractId })
        .onConflictDoUpdate({
          target: departmentContractTable.governmentContractId,
          set: { departmentId },
        });
    }

    if (agencyId) {
      await tx
        .insert(agencyContractTable)
        .values({ agencyId, governmentContractId })
        .onConflictDoUpdate({
          target: agencyContractTable.governmentContractId,
          set: { agencyId },
        });
    }

    if (officeId) {
      await tx
        .insert(officeContractTable)
        .values({ officeId, governmentContractId })
        .onConflictDoUpdate({
          target: officeContractTable.governmentContractId,
          set: { officeId },
        });
    }

    if (subOfficeId) {
      await tx
        .insert(subOfficeContractTable)
        .values({ subOfficeId, governmentContractId })
        .onConflictDoUpdate({
          target: subOfficeContractTable.governmentContractId,
          set: { subOfficeId },
        });
    }

    return { pastPerformanceId, governmentContractId };
  });
}

type AgencyInsightParams = Omit<DTO.AgencyInsightRequest, 'positionId'> & {
  positionName: string;
  positionId?: number;
};

export async function createAgencyInsight(
  db: DrizzleSupabaseClient,
  params: AgencyInsightParams
) {
  const { positionName, ...data } = params;
  let { positionId } = params;

  return await db.transaction(async (tx) => {
    if (!positionId) {
      positionId = await getOrCreatePosition(tx, positionName);
    }

    if (!positionId) {
      throw new Error('❌ FAILED TO CREATE POSITION');
    }

    const [newAgencyInsight] = await tx
      .insert(agencyInsightTable)
      .values({ positionId, ...data })
      .returning({ id: agencyInsightTable.id });

    return newAgencyInsight?.id;
  });
}

export async function updateAgencyInsight(
  db: DrizzleSupabaseClient,
  params: AgencyInsightParams & { agencyInsightId: number }
) {
  const { agencyInsightId, positionName, ...data } = params;
  let { positionId } = params;

  return await db.transaction(async (tx) => {
    if (!positionId) {
      positionId = await getOrCreatePosition(tx, positionName);
    }

    if (!positionId) {
      throw new Error('❌ FAILED TO CREATE POSITION');
    }

    const [newAgencyInsight] = await tx
      .update(agencyInsightTable)
      .set({ positionId, ...data })
      .where(eq(agencyInsightTable.id, agencyInsightId));

    return newAgencyInsight?.id;
  });
}

type EducationParams = WithUserId<{
  schoolId: number;
  degrees: {
    degreeId: number;
    startDate: string;
    endDate?: string;
    currentlyStudying?: boolean;
    majorSubjectIds: number[];
    minorSubjectIds?: number[];
  }[];
}>;

export async function createEducation(
  db: DrizzleSupabaseClient,
  params: EducationParams
) {
  const { userId, schoolId, degrees } = params;

  return await db.transaction(async (tx) => {
    const [newEducation] = await tx
      .insert(educationTable)
      .values({ userId, schoolId })
      .returning({ educationId: educationTable.id });

    const { educationId } = newEducation ?? {};

    if (!educationId) {
      throw new Error('❌ FAILED TO CREATE EDUCATION');
    }

    const userDegrees = await tx
      .insert(userDegreeTable)
      .values(
        degrees.map((degree) => ({
          userId,
          educationId,
          degreeId: degree.degreeId,
          startDate: degree.startDate,
          endDate: degree.endDate,
          currentlyStudying: degree.currentlyStudying,
        }))
      )
      .returning({ userDegreeId: userDegreeTable.id });

    if (!userDegrees.length) {
      throw new Error('❌ FAILED TO CREATE USER DEGREES');
    }

    const validUserDegrees = userDegrees
      .map((deg) => deg.userDegreeId)
      .filter((id): id is number => id !== undefined);

    const majorValues = degrees.flatMap((degree, index) => {
      const userDegreeId = validUserDegrees[index];
      if (!userDegreeId) return [];
      return (degree.majorSubjectIds ?? []).map((subjectId) => ({
        userDegreeId,
        subjectId,
      }));
    });

    const minorValues = degrees.flatMap((degree, index) => {
      const userDegreeId = validUserDegrees[index];
      if (!userDegreeId) return [];
      return (degree.minorSubjectIds ?? []).map((subjectId) => ({
        userDegreeId,
        subjectId,
      }));
    });

    if (majorValues.length) {
      await tx.insert(majorTable).values(majorValues);
    }

    if (minorValues.length) {
      await tx.insert(minorTable).values(minorValues);
    }

    return educationId;
  });
}

export async function updateEducation(
  db: DrizzleSupabaseClient,
  params: EducationParams & { educationId: number }
) {
  const { userId, educationId, schoolId, degrees } = params;

  return await db.transaction(async (tx) => {
    await tx
      .update(educationTable)
      .set({ schoolId })
      .where(eq(educationTable.id, educationId));

    const [existingUserDegrees, existingMajors, existingMinors] =
      await Promise.all([
        tx
          .select({
            id: userDegreeTable.id,
            degreeId: userDegreeTable.degreeId,
          })
          .from(userDegreeTable)
          .where(eq(userDegreeTable.educationId, educationId)),
        tx
          .select({
            userDegreeId: majorTable.userDegreeId,
            subjectId: majorTable.subjectId,
          })
          .from(majorTable)
          .innerJoin(
            userDegreeTable,
            eq(majorTable.userDegreeId, userDegreeTable.id)
          )
          .where(eq(userDegreeTable.educationId, educationId)),
        tx
          .select({
            userDegreeId: minorTable.userDegreeId,
            subjectId: minorTable.subjectId,
          })
          .from(minorTable)
          .innerJoin(
            userDegreeTable,
            eq(minorTable.userDegreeId, userDegreeTable.id)
          )
          .where(eq(userDegreeTable.educationId, educationId)),
      ]);

    const existingDegreeMap = new Map(
      existingUserDegrees.map((degree) => [degree.degreeId, degree])
    );
    const existingMajorsMap = new Map<number, number[]>();
    const existingMinorsMap = new Map<number, number[]>();

    for (const major of existingMajors) {
      const subjects = existingMajorsMap.get(major.userDegreeId) ?? [];
      subjects.push(major.subjectId);
      existingMajorsMap.set(major.userDegreeId, subjects);
    }

    for (const minor of existingMinors) {
      const subjects = existingMinorsMap.get(minor.userDegreeId) ?? [];
      subjects.push(minor.subjectId);
      existingMinorsMap.set(minor.userDegreeId, subjects);
    }

    const userDegreesToKeep = new Set<number>();
    const userDegreesToAdd: EducationParams['degrees'] = [];
    const subjectUpdates: Promise<unknown>[] = [];
    const degreeUpdates: Promise<unknown>[] = [];

    for (const degree of degrees) {
      const {
        degreeId,
        startDate,
        endDate,
        currentlyStudying,
        majorSubjectIds = [],
        minorSubjectIds = [],
      } = degree;

      const existingDegree = existingDegreeMap.get(degreeId);

      if (existingDegree) {
        userDegreesToKeep.add(existingDegree.id);

        degreeUpdates.push(
          tx
            .update(userDegreeTable)
            .set({ startDate, endDate, currentlyStudying })
            .where(eq(userDegreeTable.id, existingDegree.id))
        );

        const existingMajorIds = existingMajorsMap.get(existingDegree.id) ?? [];
        const existingMinorIds = existingMinorsMap.get(existingDegree.id) ?? [];

        const majorsToAdd = majorSubjectIds.filter(
          (id) => !existingMajorIds.includes(id)
        );
        const majorsToRemove = existingMajorIds.filter(
          (id) => !majorSubjectIds.includes(id)
        );
        const minorsToAdd = minorSubjectIds.filter(
          (id) => !existingMinorIds.includes(id)
        );
        const minorsToRemove = existingMinorIds.filter(
          (id) => !minorSubjectIds.includes(id)
        );

        if (majorsToRemove.length)
          subjectUpdates.push(
            tx
              .delete(majorTable)
              .where(
                and(
                  eq(majorTable.userDegreeId, existingDegree.id),
                  inArray(majorTable.subjectId, majorsToRemove)
                )
              )
          );

        if (minorsToRemove.length)
          subjectUpdates.push(
            tx
              .delete(minorTable)
              .where(
                and(
                  eq(minorTable.userDegreeId, existingDegree.id),
                  inArray(minorTable.subjectId, minorsToRemove)
                )
              )
          );

        if (majorsToAdd.length)
          subjectUpdates.push(
            tx.insert(majorTable).values(
              majorsToAdd.map((subjectId) => ({
                userDegreeId: existingDegree.id,
                subjectId,
              }))
            )
          );

        if (minorsToAdd.length)
          subjectUpdates.push(
            tx.insert(minorTable).values(
              minorsToAdd.map((subjectId) => ({
                userDegreeId: existingDegree.id,
                subjectId,
              }))
            )
          );
      } else {
        userDegreesToAdd.push(degree);
      }
    }

    const userDegreesToDelete = existingUserDegrees
      .filter((userDegree) => !userDegreesToKeep.has(userDegree.id))
      .map((userDegree) => userDegree.id);

    if (userDegreesToDelete.length)
      subjectUpdates.push(
        tx
          .delete(userDegreeTable)
          .where(inArray(userDegreeTable.id, userDegreesToDelete))
      );

    await Promise.all([...subjectUpdates, ...degreeUpdates]);

    const userDegrees = await tx
      .insert(userDegreeTable)
      .values(
        userDegreesToAdd.map((degree) => ({
          userId,
          educationId,
          degreeId: degree.degreeId,
          startDate: degree.startDate,
          endDate: degree.endDate,
          currentlyStudying: degree.currentlyStudying,
        }))
      )
      .returning({ userDegreeId: userDegreeTable.id });

    if (!userDegrees.length) {
      throw new Error('❌ FAILED TO CREATE USER DEGREES');
    }

    const validUserDegrees = userDegrees
      .map((deg) => deg.userDegreeId)
      .filter((id): id is number => id !== undefined);

    const majorValues = userDegreesToAdd.flatMap((degree, index) => {
      const userDegreeId = validUserDegrees[index];
      if (!userDegreeId) return [];
      return (degree.majorSubjectIds ?? []).map((subjectId) => ({
        userDegreeId,
        subjectId,
      }));
    });

    const minorValues = userDegreesToAdd.flatMap((degree, index) => {
      const userDegreeId = validUserDegrees[index];
      if (!userDegreeId) return [];
      return (degree.minorSubjectIds ?? []).map((subjectId) => ({
        userDegreeId,
        subjectId,
      }));
    });

    if (majorValues.length) {
      await tx.insert(majorTable).values(majorValues);
    }

    if (minorValues.length) {
      await tx.insert(minorTable).values(minorValues);
    }

    return educationId;
  });
}

export async function upsertExpertise(
  db: DrizzleSupabaseClient,
  params: WithUserId<{
    expertise: Omit<DTO.UserSkillRequest, 'userId'>[];
  }>
) {
  const { userId, expertise } = params;

  return await db.transaction(async (tx) => {
    const existingExpertise = await tx
      .select({
        skillId: userSkillTable.skillId,
        expertiseLevel: userSkillTable.expertiseLevel,
      })
      .from(userSkillTable)
      .where(eq(userSkillTable.userId, userId));

    const existingMap = new Map(
      existingExpertise.map((e) => [`${e.skillId}-${e.expertiseLevel}`, e])
    );
    const newMap = new Map(
      expertise.map((e) => [`${e.skillId}-${e.expertiseLevel}`, e])
    );

    const expertiseToInsert = expertise
      .filter((e) => !existingMap.has(`${e.skillId}-${e.expertiseLevel}`))
      .map((e) => ({
        expertiseLevel: e.expertiseLevel,
        skillId: e.skillId,
        userId,
      }));

    const expertiseToDelete = existingExpertise
      .filter((e) => !newMap.has(`${e.skillId}-${e.expertiseLevel}`))
      .map((e) => e.skillId);

    if (expertiseToDelete.length > 0) {
      await tx
        .delete(userSkillTable)
        .where(
          and(
            eq(userSkillTable.userId, userId),
            inArray(userSkillTable.skillId, expertiseToDelete)
          )
        );
    }

    if (expertiseToInsert.length > 0) {
      await tx.insert(userSkillTable).values(expertiseToInsert);
    }
    return [...expertiseToDelete, ...expertiseToInsert.map((e) => e.skillId)];
  });
}

export async function createUserCertification(
  db: DrizzleSupabaseClient,
  params: DTO.CertificationRequest &
    Omit<DTO.UserCertificationRequest, 'certificationId'>
) {
  const {
    userId,
    name,
    provider,
    issuedDate,
    expirationDate,
    credentialId,
    credentialUrl,
  } = params;

  return await db.transaction(async (tx) => {
    const [newCertification] = await tx
      .insert(certificationTable)
      .values({ name, provider })
      .returning({ certificationId: certificationTable.id });

    const { certificationId } = newCertification ?? {};

    if (!certificationId) {
      throw new Error('❌ FAILED TO CREATE CERTIFICATION');
    }

    await tx.insert(userCertificationTable).values({
      userId,
      certificationId,
      credentialId,
      credentialUrl,
      issuedDate,
      expirationDate,
    });

    return certificationId;
  });
}

export async function updateUserCertification(
  db: DrizzleSupabaseClient,
  params: DTO.CertificationRequest & DTO.UserCertificationRequest
) {
  const {
    certificationId,
    name,
    provider,
    issuedDate,
    expirationDate,
    credentialId,
    credentialUrl,
  } = params;

  return await db.transaction(async (tx) => {
    await tx
      .update(certificationTable)
      .set({ name, provider })
      .where(eq(certificationTable.id, certificationId));

    await tx
      .update(userCertificationTable)
      .set({ credentialId, credentialUrl, issuedDate, expirationDate })
      .where(eq(userCertificationTable.certificationId, certificationId));

    return certificationId;
  });
}

type FullTimeRoleParams = Omit<
  DTO.FullTimeAvailabilityRequest,
  'positionId'
> & {
  positionName: string;
  positionId?: number;
};

export async function createFullTimeRole(
  db: DrizzleSupabaseClient,
  params: FullTimeRoleParams
) {
  const { userId, positionName, payRateId, desiredStartDate, locationType } =
    params;
  let { positionId } = params;

  return await db.transaction(async (tx) => {
    const [user] = await tx
      .select({ fteAvailable: usersTable.fteAvailable })
      .from(usersTable)
      .where(eq(usersTable.id, userId));

    if (!user) {
      throw new Error('❌ FAILED TO GET USER');
    }

    if (!user.fteAvailable) {
      await tx
        .update(usersTable)
        .set({ fteAvailable: true })
        .where(eq(usersTable.id, userId));
    }

    if (!positionId) {
      positionId = await getOrCreatePosition(tx, positionName);
    }

    if (!positionId) {
      throw new Error('❌ FAILED TO CREATE POSITION');
    }

    const [newFullTimeRole] = await tx
      .insert(fullTimeAvailabilityTable)
      .values({
        userId,
        positionId,
        payRateId,
        desiredStartDate,
        locationType,
      })
      .returning({ fullTimeAvailabilityId: fullTimeAvailabilityTable.id });

    const { fullTimeAvailabilityId } = newFullTimeRole ?? {};

    if (!fullTimeAvailabilityId) {
      throw new Error('❌ FAILED TO CREATE FULL TIME ROLE');
    }

    return fullTimeAvailabilityId;
  });
}

export async function updateFullTimeRole(
  db: DrizzleSupabaseClient,
  params: FullTimeRoleParams & { fullTimeAvailabilityId: number }
) {
  const {
    fullTimeAvailabilityId,
    positionName,
    payRateId,
    desiredStartDate,
    locationType,
  } = params;
  let { positionId } = params;

  return await db.transaction(async (tx) => {
    if (!positionId) {
      positionId = await getOrCreatePosition(tx, positionName);
    }

    if (!positionId) {
      throw new Error('❌ FAILED TO CREATE POSITION');
    }

    await tx
      .update(fullTimeAvailabilityTable)
      .set({ positionId, payRateId, locationType, desiredStartDate })
      .where(eq(fullTimeAvailabilityTable.id, fullTimeAvailabilityId));

    return fullTimeAvailabilityId;
  });
}

export async function deleteFullTimeRole(
  db: DrizzleSupabaseClient,
  params: { fullTimeAvailabilityId: number }
) {
  const { fullTimeAvailabilityId } = params;

  return await db.transaction(async (tx) => {
    const [deletedFullTimeAvailability] = await tx
      .delete(fullTimeAvailabilityTable)
      .where(eq(fullTimeAvailabilityTable.id, fullTimeAvailabilityId))
      .returning({ userId: fullTimeAvailabilityTable.userId });

    const { userId } = deletedFullTimeAvailability ?? {};

    if (!userId) {
      throw new Error('❌ FAILED TO DELETE FULL TIME AVAILABILITY');
    }

    const [remainingRoles] = await tx
      .select({ count: sql<number>`count(*)`.mapWith(Number) })
      .from(fullTimeAvailabilityTable)
      .where(eq(fullTimeAvailabilityTable.userId, userId));

    const { count } = remainingRoles ?? {};

    if (count === 0) {
      await tx
        .update(usersTable)
        .set({ fteAvailable: false })
        .where(eq(usersTable.id, userId));
    }

    return fullTimeAvailabilityId;
  });
}

export async function insertFeedback(
  db: DrizzleSupabaseClient,
  params: DTO.FeedbackRequest & { fileUrls?: string[] }
) {
  const {
    userId,
    type,
    rating,
    title,
    description,
    issueType,
    fileUrls = [],
  } = params;

  return await db.transaction(async (tx) => {
    const [newFeedback] = await tx
      .insert(feedbackTable)
      .values({ userId, rating, title, description, type, issueType })
      .returning({ feedbackId: feedbackTable.id });

    const { feedbackId } = newFeedback ?? {};

    if (!feedbackId) {
      throw new Error('❌ FAILED TO CREATE FEEDBACK');
    }

    if (fileUrls.length > 0) {
      await tx
        .insert(feedbackFileTable)
        .values(fileUrls.map((fileUrl) => ({ feedbackId, fileUrl })));
    }

    return { feedbackId };
  });
}

export async function createUserReview(
  db: DrizzleSupabaseClient,
  params: DTO.UserReviewRequest & { userReviewRequestId: number }
) {
  const {
    userReviewRequestId,
    reviewerId,
    status = 'PENDING',
    ...data
  } = params;

  return await db.transaction(async (tx) => {
    await tx
      .delete(userReviewRequestTable)
      .where(
        and(
          eq(userReviewRequestTable.id, userReviewRequestId),
          eq(userReviewRequestTable.reviewerEmail, data.reviewerEmail)
        )
      );

    const [userReview] = await tx
      .insert(userReviewTable)
      .values({
        ...data,
        reviewerId,
        status,
      })
      .returning({ userReviewId: userReviewTable.id });

    const { userReviewId } = userReview ?? {};

    if (!userReviewId) {
      throw new Error('❌ FAILED TO CREATE USER REVIEW');
    }

    if (!!reviewerId) {
      const [user] = await tx
        .select({
          avatar: usersTable.avatar,
          username: usersTable.username,
        })
        .from(usersTable)
        .where(eq(usersTable.id, reviewerId));

      return {
        userReviewId,
        user,
      };
    }

    return { userReviewId };
  });
}

export async function createConnectionRequest(
  db: DrizzleSupabaseClient,
  params: { requesterId: string; receiverId: string }
) {
  const { requesterId, receiverId } = params;

  return await db.transaction(async (tx) => {
    const existingConnection = await tx
      .select({ exists: sql<number>`1` })
      .from(connectionTable)
      .where(
        or(
          and(
            eq(connectionTable.user1Id, requesterId),
            eq(connectionTable.user2Id, receiverId)
          ),
          and(
            eq(connectionTable.user1Id, receiverId),
            eq(connectionTable.user2Id, requesterId)
          )
        )
      )
      .limit(1);

    if (existingConnection.length > 0) {
      return;
    }

    const [newConnectionRequest] = await tx
      .insert(connectionRequestTable)
      .values({ requesterId, receiverId })
      .returning({ connectionRequestId: connectionRequestTable.id });

    const { connectionRequestId } = newConnectionRequest ?? {};

    if (!connectionRequestId) {
      throw new Error('❌ FAILED TO CREATE CONNECTION REQUEST');
    }

    return connectionRequestId;
  });
}

export async function acceptConnectionRequest(
  db: DrizzleSupabaseClient,
  params: {
    connectionRequestId: number;
    requesterId: string;
    receiverId: string;
  }
) {
  const { connectionRequestId, requesterId, receiverId } = params;

  return await db.transaction(async (tx) => {
    await tx
      .delete(connectionRequestTable)
      .where(
        or(
          eq(connectionRequestTable.id, connectionRequestId),
          and(
            eq(connectionRequestTable.requesterId, receiverId),
            eq(connectionRequestTable.receiverId, requesterId)
          ),
          and(
            eq(connectionRequestTable.requesterId, requesterId),
            eq(connectionRequestTable.receiverId, receiverId)
          )
        )
      )
      .returning({ id: connectionRequestTable.id });

    const user1Id = requesterId < receiverId ? requesterId : receiverId;
    const user2Id = requesterId < receiverId ? receiverId : requesterId;
    const initiatedByUser1 = requesterId === user1Id;

    const [newConnection] = await tx
      .insert(connectionTable)
      .values({ user1Id, user2Id, initiatedByUser1 })
      .returning({ connectionId: connectionTable.id });

    const { connectionId } = newConnection ?? {};

    if (!connectionId) {
      throw new Error('❌ FAILED TO CREATE CONNECTION');
    }

    let chatRoomId = await getChatRoomIdForUsers(tx, { user1Id, user2Id });

    if (chatRoomId) {
      await tx
        .update(chatRoomTable)
        .set({ isDisabled: false, lastMessageAt: new Date() })
        .where(eq(chatRoomTable.id, chatRoomId));
    } else {
      const [newChatRoom] = await tx
        .insert(chatRoomTable)
        .values({ creatorId: requesterId })
        .returning({ chatRoomId: chatRoomTable.id });

      chatRoomId = newChatRoom?.chatRoomId;

      if (!chatRoomId) {
        throw new Error('❌ FAILED TO CREATE CHAT ROOM');
      }

      await tx.insert(chatRoomMemberTable).values([
        { roomId: chatRoomId, userId: user1Id },
        { roomId: chatRoomId, userId: user2Id },
      ]);
    }

    const [receiver] = await tx
      .select({
        avatar: usersTable.avatar,
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        username: usersTable.username,
      })
      .from(usersTable)
      .where(eq(usersTable.id, receiverId));

    return { connectionId, chatRoomId, receiver };
  });
}

export async function deleteConnection(
  db: DrizzleSupabaseClient,
  params: { connectionId: number }
) {
  const { connectionId } = params;

  return await db.transaction(async (tx) => {
    const [deletedConnection] = await tx
      .delete(connectionTable)
      .where(eq(connectionTable.id, connectionId))
      .returning({
        user1Id: connectionTable.user1Id,
        user2Id: connectionTable.user2Id,
      });

    const { user1Id, user2Id } = deletedConnection ?? {};

    if (!user1Id || !user2Id) {
      return { chatRoomId: undefined };
    }

    const chatRoomId = await getChatRoomIdForUsers(tx, { user1Id, user2Id });

    if (chatRoomId) {
      await tx
        .update(chatRoomTable)
        .set({ isDisabled: true })
        .where(eq(chatRoomTable.id, chatRoomId));
    }

    return { chatRoomId };
  });
}

export async function sendInitialMessage(
  db: DrizzleSupabaseClient,
  params: {
    roomId: string;
    receiverId: string;
    senderId: string;
    content: string;
    attachments?: DTO.AttachmentMetadata[];
  }
) {
  const { roomId, senderId, receiverId, content, attachments } = params;

  return await db.transaction(async (tx) => {
    await tx.insert(chatRoomTable).values({ id: roomId, creatorId: senderId });

    await tx.insert(chatRoomMemberTable).values([
      {
        roomId,
        userId: receiverId,
        hasRead: false,
      },
      {
        roomId,
        userId: senderId,
        hasRead: true,
      },
    ]);

    const newMessage = await tx
      .insert(messageTable)
      .values({
        roomId,
        content,
        senderId,
        attachments,
        ablyTimeserial: crypto.randomUUID(),
      })
      .returning({ messageId: messageTable.id });

    const { messageId } = newMessage[0]!;

    return { roomId, messageId };
  });
}

export async function sendMessage(
  db: DrizzleSupabaseClient,
  params: {
    roomId: string;
    senderId: string;
    content?: string;
    ablyTimeserial?: string;
    attachments?: DTO.AttachmentMetadata[];
    offlineUsers?: string[];
  }
) {
  const {
    roomId,
    senderId,
    content,
    ablyTimeserial,
    attachments,
    offlineUsers = [],
  } = params;

  return await db.transaction(async (tx) => {
    const [newMessage] = await tx
      .insert(messageTable)
      .values({
        roomId,
        content,
        senderId,
        attachments,
        ablyTimeserial,
      })
      .returning({ messageId: messageTable.id });

    const { messageId } = newMessage ?? {};

    if (!messageId) {
      throw new Error('❌ FAILED TO CREATE MESSAGE');
    }

    await tx
      .update(chatRoomTable)
      .set({ lastMessageAt: new Date() })
      .where(eq(chatRoomTable.id, roomId));

    if (offlineUsers) {
      await tx
        .update(chatRoomMemberTable)
        .set({ hasRead: false })
        .where(
          and(
            eq(chatRoomMemberTable.roomId, roomId),
            inArray(chatRoomMemberTable.userId, offlineUsers)
          )
        );
    }

    return messageId;
  });
}

export async function approveOrganizationJoinRequests(
  db: DrizzleSupabaseClient,
  params: {
    orgJoinRequestIds: number[];
    processedByUserId: string;
    organizationId: string;
    roleId: MembershipRole;
  }
): Promise<string[]> {
  const { orgJoinRequestIds, processedByUserId, organizationId, roleId } =
    params;

  if (!orgJoinRequestIds.length) {
    throw new Error('NO ORGANIZATION JOIN REQUESTS PROVIDED');
  }

  if (roleId < MembershipRole.Member || roleId > MembershipRole.Admin) {
    throw new Error('INVALID ROLE_ID. MUST BE 0 (Member) OR 1 (Admin)');
  }

  return await db.transaction(async (tx) => {
    const approvedUsersEmails: string[] = [];

    for (const orgJoinRequestId of orgJoinRequestIds) {
      try {
        const [updatedOrgJoinRequest] = await tx
          .update(organizationJoinRequestTable)
          .set({ status: 'APPROVED', processedBy: processedByUserId })
          .where(
            and(
              eq(organizationJoinRequestTable.id, orgJoinRequestId),
              eq(organizationJoinRequestTable.status, 'PENDING'),
              eq(organizationJoinRequestTable.organizationId, organizationId)
            )
          )
          .returning({
            email: organizationJoinRequestTable.email,
            userId: organizationJoinRequestTable.userId,
          });

        const { email, userId } = updatedOrgJoinRequest ?? {};

        if (!userId || !email) {
          continue;
        }

        const userHasMembership = await tx
          .select({ exists: sql<number>`1` })
          .from(membershipTable)
          .where(
            and(
              eq(membershipTable.organizationId, organizationId),
              eq(membershipTable.userId, userId)
            )
          )
          .then((result) => result.length > 0);

        if (userHasMembership) {
          await tx
            .update(membershipTable)
            .set({ roleId })
            .where(
              and(
                eq(membershipTable.organizationId, organizationId),
                eq(membershipTable.userId, userId)
              )
            );
        } else {
          await tx
            .insert(membershipTable)
            .values({ organizationId, userId, roleId });
        }

        const [authUser] = await tx
          .select({ rawAppMetaData: authUsersTable.rawAppMetaData })
          .from(authUsersTable)
          .where(eq(authUsersTable.email, email));

        const { rawAppMetaData } = authUser ?? {};

        if (rawAppMetaData?.isClient !== true) {
          await tx
            .update(authUsersTable)
            .set({ rawAppMetaData: { ...rawAppMetaData, isClient: true } })
            .where(eq(authUsersTable.email, email));
        }

        approvedUsersEmails.push(email);
      } catch (error) {
        logger.error(`❌ ERROR PROCESSING REQUEST ID ${orgJoinRequestId}:`, {
          error,
        });
      }
    }

    return approvedUsersEmails;
  });
}

export function upsertOrganizationSocialLink(
  db: DrizzleSupabaseClient,
  params: {
    userId: string;
    urls: string[];
  }
) {
  const { userId, urls } = params;

  return db.transaction(async (tx) => {
    const [organization] = await tx
      .select({ id: organizationTable.id })
      .from(membershipTable)
      .innerJoin(
        organizationTable,
        eq(membershipTable.organizationId, organizationTable.id)
      )
      .where(eq(membershipTable.userId, userId))
      .limit(1);

    const { id: organizationId } = organization ?? {};

    if (!organizationId) {
      logger.error({ organizationId }, '❌ ERROR GETTING ORGANIZATION ID');
      throw new Error('❌ ERROR GETTING ORGANIZATION ID');
    }

    await tx
      .insert(socialLinkTable)
      .values(
        urls.map((url) => ({
          organizationId,
          userId,
          url,
        }))
      )
      .onConflictDoNothing({
        target: [socialLinkTable.organizationId, socialLinkTable.url],
      });
  });
}
