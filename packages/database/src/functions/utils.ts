import type { TransactionType } from '../index';

import { capitalizeWords } from '@packages/utils/text';
import { eq } from 'drizzle-orm';

import { positionTable } from '../schema/tables';

export async function getOrCreatePosition(
  tx: TransactionType,
  positionName: string
) {
  const [position] = await tx
    .insert(positionTable)
    .values({ name: capitalizeWords(positionName) })
    .onConflictDoNothing({ target: positionTable.name })
    .returning({ positionId: positionTable.id });

  if (!position) {
    const [existingPosition] = await tx
      .select({ positionId: positionTable.id })
      .from(positionTable)
      .where(eq(positionTable.name, capitalizeWords(positionName)));

    return existingPosition?.positionId;
  }
  return position?.positionId;
}
