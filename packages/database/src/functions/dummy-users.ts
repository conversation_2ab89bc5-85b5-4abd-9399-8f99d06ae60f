import type { DrizzleSupabaseClient } from '../index';
import type {
  EmploymentTypeEnumType,
  ExpertiseLevelEnumType,
  LocationTypeEnumType,
  RelationshipStrengthTypeEnumType,
} from '../schema/types';

import { eq } from 'drizzle-orm';

import { MembershipRole } from '@packages/database/enums';
import {
  addressTable,
  agencyContractTable,
  agencyInsightTable,
  certificationTable,
  departmentContractTable,
  educationTable,
  experiencePositionTable,
  experienceSkillTable,
  experienceTable,
  fullTimeAvailabilityTable,
  governmentContractTable,
  majorTable,
  membershipTable,
  minorTable,
  officeContractTable,
  organizationAddressTable,
  organizationTable,
  pastPerformanceTable,
  socialLinkTable,
  subOfficeContractTable,
  userAddressTable,
  userCertificationTable,
  userDegreeTable,
  userPastPerformanceTable,
  userServiceTable,
  userSkillTable,
  usersTable,
} from '../schema/tables';

// GENERATE RANDOM CONSULTANT
export async function createDummyConsultant(
  db: DrizzleSupabaseClient,
  params: {
    userId: string;
    userAvatar: string;
    userFirstName: string;
    userLastName: string;
    userHeadline: string;
    userState: string;
    userCity: string;
    orgLogo: string;
    orgName: string;
    orgDescription: string;
    orgAddress1: string;
    orgState: string;
    orgCity: string;
    expCompanyName: string;
    expDescription: string;
    expState: string;
    expCity: string;
    govContractName: string;
    govContractDescription: string;
    pastPerformanceDescription: string;
    agencyInsightDescription: string;
    certificationName: string;
    certificationProvider: string;
  }
) {
  const {
    userId,
    userAvatar,
    userFirstName,
    userLastName,
    userHeadline,
    userState,
    userCity,
    orgLogo,
    orgName,
    orgDescription,
    orgAddress1,
    orgState,
    orgCity,
    expCompanyName,
    expDescription,
    expState,
    expCity,
    govContractName,
    govContractDescription,
    pastPerformanceDescription,
    agencyInsightDescription,
    certificationName,
    certificationProvider,
  } = params;

  // Constants
  const HOURLY_PAY_RATE_START_ID = 15;
  const HOURLY_PAY_RATE_END_ID = 32;
  const SKILL_LEN = 145;
  const POSITION_LEN = 112;
  const DEPARTMENT_LEN = 18;
  const AGENCY_LEN = 426;
  const OFFICE_LEN = 1413;
  const SUB_OFFICE_LEN = 826;
  const SERVICE_LEN = 10;
  const SCHOOL_LEN = 10029;
  const DEGREE_LEN = 148;
  const SUBJECT_LEN = 405;

  // Variables
  const userUsername = `${userFirstName}_${userLastName}_${genRandomNumberOfLen(5)}`;
  const credentialId = crypto.randomUUID();
  const isPriceFixed = genRandomBoolean();
  const isCurrentlyStudying = genRandomBoolean();

  return await db.transaction(async (tx) => {
    // Update user
    await tx
      .update(usersTable)
      .set({
        avatar: userAvatar,
        firstName: userFirstName,
        lastName: userLastName,
        username: userUsername,
        headline: userHeadline,
        obEmailSent: true,
        fteAvailable: true,
        isPublic: true,
        isAvailable: true,
        phoneNumber: genRandomPhoneNumber(),
        legalStatus: getRandomOption([
          'US_CITIZEN',
          'PERMANENT_RESIDENT',
          'H1B_VISA',
          'OTHER',
        ]),
        securityClearance: getRandomOption([
          'PUBLIC_TRUST',
          'CONFIDENTIAL',
          'SECRET',
          'TOP_SECRET',
          'TOP_SECRET_SCI',
          'DOE_Q',
          'DOE_L',
          'INTEL',
          'UNSPECIFIED',
        ]),
        payRateId: genRandomNumberBetween(
          HOURLY_PAY_RATE_START_ID,
          HOURLY_PAY_RATE_END_ID
        ),
      })
      .where(eq(usersTable.id, userId));

    // User address
    const [userAddress] = await tx
      .insert(addressTable)
      .values({ city: userCity, state: userState })
      .returning({ userAddressId: addressTable.id });

    const { userAddressId } = userAddress ?? {};

    if (!userAddressId) {
      throw new Error('❌ FAILED TO CREATE ADDRESS');
    }

    await tx.insert(userAddressTable).values({
      addressId: userAddressId,
      userId,
    });

    // Social links
    const socialLinkCount = genRandomNumberBetween(1, 3);
    await tx.insert(socialLinkTable).values(
      Array.from({ length: socialLinkCount }).map((i) => ({
        url: genRandomSocialUrl(`${userUsername}_${i}`),
        userId,
      }))
    );

    // Organization
    const [organization] = await tx
      .insert(organizationTable)
      .values({
        logo: orgLogo,
        name: orgName,
        description: orgDescription,
      })
      .returning({ organizationId: organizationTable.id });

    const { organizationId } = organization ?? {};

    if (!organizationId) {
      throw new Error('❌ FAILED TO CREATE ORGANIZATION');
    }

    // Membership
    await tx.insert(membershipTable).values({
      userId,
      organizationId,
      roleId: MembershipRole.Owner,
    });

    // Organization address
    const [orgAddress] = await tx
      .insert(addressTable)
      .values({
        address1: orgAddress1,
        city: orgCity,
        state: orgState,
        zipCode: genRandomNumberOfLen(5),
      })
      .returning({ orgAddressId: addressTable.id });

    const { orgAddressId } = orgAddress ?? {};

    if (!orgAddressId) {
      throw new Error('❌ FAILED TO CREATE ADDRESS');
    }

    await tx.insert(organizationAddressTable).values({
      addressId: orgAddressId,
      organizationId,
    });

    const isCurrentRole = genRandomBoolean();

    // Work experience
    const [experience] = await tx
      .insert(experienceTable)
      .values({
        userId,
        companyName: expCompanyName,
        description: expDescription,
        city: expCity,
        state: expState,
        startDate: genRandomTimestamp(),
        endDate: !isCurrentRole ? genRandomTimestamp() : null,
        isCurrentRole,
        locationType: getRandomOption([
          'ONSITE',
          'HYBRID',
          'REMOTE',
        ]) as LocationTypeEnumType,
        employmentType: getRandomOption([
          'FULL-TIME',
          'PART-TIME',
          'SELF-EMPLOYED',
          'FREELANCE',
          'CONTRACT',
          'INTERNSHIP',
          'APPRENTICESHIP',
          'SEASONAL',
        ]) as EmploymentTypeEnumType,
      })
      .returning({ experienceId: experienceTable.id });

    const { experienceId } = experience ?? {};

    if (!experienceId) {
      throw new Error('❌ FAILED TO CREATE EXPERIENCE');
    }

    // Experience skills
    const skillCount = genRandomNumberBetween(3, 5);
    await tx.insert(experienceSkillTable).values(
      Array.from({ length: skillCount }).map(() => ({
        experienceId,
        skillId: genRandomNumberBetween(1, SKILL_LEN),
      }))
    );

    // Experience position
    await tx.insert(experiencePositionTable).values({
      experienceId,
      positionId: genRandomNumberBetween(1, POSITION_LEN),
    });

    // Government contract
    const [govContract] = await tx
      .insert(governmentContractTable)
      .values({
        name: govContractName,
        description: govContractDescription,
        value: genRandomNumberBetween(20, 1000).toString(),
        rfpNumber: `RFP-${genRandomNumberOfLen(3)}`,
      })
      .returning({ governmentContractId: governmentContractTable.id });

    const { governmentContractId } = govContract ?? {};

    if (!governmentContractId) {
      throw new Error('❌ FAILED TO CREATE GOVERNMENT CONTRACT');
    }

    // Department contract
    await tx.insert(departmentContractTable).values({
      governmentContractId,
      departmentId: genRandomNumberBetween(1, DEPARTMENT_LEN),
    });

    // Agency contract
    await tx.insert(agencyContractTable).values({
      governmentContractId,
      agencyId: genRandomNumberBetween(1, AGENCY_LEN),
    });

    // Office contract
    await tx.insert(officeContractTable).values({
      governmentContractId,
      officeId: genRandomNumberBetween(1, OFFICE_LEN),
    });

    // Sub-office contract
    await tx.insert(subOfficeContractTable).values({
      governmentContractId,
      subOfficeId: genRandomNumberBetween(1, SUB_OFFICE_LEN),
    });

    // Past performance
    const [pastPerformance] = await tx
      .insert(pastPerformanceTable)
      .values({
        governmentContractId,
        description: pastPerformanceDescription,
        positionId: genRandomNumberBetween(1, POSITION_LEN),
        awardDate: genRandomTimestamp(),
      })
      .returning({ pastPerformanceId: pastPerformanceTable.id });

    const { pastPerformanceId } = pastPerformance ?? {};

    if (!pastPerformanceId) {
      throw new Error('❌ FAILED TO CREATE PAST PERFORMANCE');
    }

    // User past performance
    await tx.insert(userPastPerformanceTable).values({
      userId,
      pastPerformanceId,
    });

    // Agency insights
    await tx.insert(agencyInsightTable).values({
      userId,
      description: agencyInsightDescription,
      departmentId: genRandomNumberBetween(1, DEPARTMENT_LEN),
      agencyId: genRandomNumberBetween(1, AGENCY_LEN),
      officeId: genRandomNumberBetween(1, OFFICE_LEN),
      positionId: genRandomNumberBetween(1, POSITION_LEN),
      relationshipStrength: getRandomOption([
        'ACQUAINTANCE',
        'OCCASIONAL',
        'REGULAR',
        'STRONG',
        'TRUSTED',
      ]) as RelationshipStrengthTypeEnumType,
    });

    // Education
    const [education] = await tx
      .insert(educationTable)
      .values({
        userId,
        schoolId: genRandomNumberBetween(1, SCHOOL_LEN),
      })
      .returning({ educationId: educationTable.id });

    const { educationId } = education ?? {};

    if (!educationId) {
      throw new Error('❌ FAILED TO CREATE EDUCATION');
    }

    // User degree
    const [userDegree] = await tx
      .insert(userDegreeTable)
      .values({
        userId,
        educationId,
        startDate: genRandomTimestamp(),
        degreeId: genRandomNumberBetween(1, DEGREE_LEN),
        endDate: isCurrentlyStudying ? null : genRandomTimestamp(),
        currentlyStudying: isCurrentlyStudying,
      })
      .returning({ userDegreeId: userDegreeTable.id });

    const { userDegreeId } = userDegree ?? {};

    if (!userDegreeId) {
      throw new Error('❌ FAILED TO CREATE USER DEGREE');
    }

    // Majors
    const majorCount = genRandomNumberBetween(1, 3);
    await tx.insert(majorTable).values(
      Array.from({ length: majorCount }).map(() => ({
        userDegreeId: userDegreeId,
        subjectId: genRandomNumberBetween(1, SUBJECT_LEN),
      }))
    );

    // Minors
    const minorCount = genRandomNumberBetween(1, 3);
    await tx.insert(minorTable).values(
      Array.from({ length: minorCount }).map(() => ({
        userDegreeId: userDegreeId,
        subjectId: genRandomNumberBetween(1, SUBJECT_LEN),
      }))
    );

    // User skills
    const userSkillCount = genRandomNumberBetween(3, 5);
    await tx.insert(userSkillTable).values(
      Array.from({ length: userSkillCount }).map(() => ({
        userId: userId,
        skillId: genRandomNumberBetween(1, SKILL_LEN),
        expertiseLevel: getRandomOption([
          'NOVICE',
          'EXPERIENCED',
          'PROFESSIONAL',
          'MAVEN',
        ]) as ExpertiseLevelEnumType,
      }))
    );

    // Certification
    const [certification] = await tx
      .insert(certificationTable)
      .values({
        name: certificationName,
        provider: certificationProvider,
      })
      .returning({ certificationId: certificationTable.id });

    const { certificationId } = certification ?? {};

    if (!certificationId) {
      throw new Error('❌ FAILED TO CREATE CERTIFICATION');
    }

    // User certification
    await tx.insert(userCertificationTable).values({
      userId,
      certificationId,
      credentialId,
      credentialUrl: genRandomUrl(credentialId),
      issuedDate: genRandomTimestamp(),
      expirationDate: genRandomBoolean() ? genRandomTimestamp() : null,
    });

    // User service
    await tx.insert(userServiceTable).values({
      userId,
      serviceId: genRandomNumberBetween(1, SERVICE_LEN),
      expertiseLevel: getRandomOption([
        'NOVICE',
        'EXPERIENCED',
        'PROFESSIONAL',
        'MAVEN',
      ]) as ExpertiseLevelEnumType,
      fixedPrice: isPriceFixed ? Number(genRandomNumberOfLen(3)) : null,
      payRateId: isPriceFixed
        ? null
        : genRandomNumberBetween(
            HOURLY_PAY_RATE_START_ID,
            HOURLY_PAY_RATE_END_ID
          ),
    });

    // Full time availability
    await tx.insert(fullTimeAvailabilityTable).values({
      userId,
      positionId: genRandomNumberBetween(1, POSITION_LEN),
      payRateId: genRandomNumberBetween(1, 14),
      desiredStartDate: genRandomTimestamp(),
      locationType: getRandomOption([
        'ONSITE',
        'HYBRID',
        'REMOTE',
      ]) as LocationTypeEnumType,
    });

    return userId;
  });
}

export async function createDummyClient(
  db: DrizzleSupabaseClient,
  params: {
    userId: string;
    userAvatar: string;
    userFirstName: string;
    userLastName: string;
    userHeadline: string;
    userState: string;
    userCity: string;
    orgLogo: string;
    orgName: string;
    orgDescription: string;
    orgAddress1: string;
    orgState: string;
    orgCity: string;
  }
) {
  const {
    userId,
    userAvatar,
    userFirstName,
    userLastName,
    userHeadline,
    userState,
    userCity,
    orgLogo,
    orgName,
    orgDescription,
    orgAddress1,
    orgState,
    orgCity,
  } = params;

  return await db.transaction(async (tx) => {
    const userUsername = `${userFirstName}_${userLastName}_${genRandomNumberOfLen(5)}`;

    // Update user
    await tx
      .update(usersTable)
      .set({
        avatar: userAvatar,
        firstName: userFirstName,
        lastName: userLastName,
        username: userUsername,
        headline: userHeadline,
        obEmailSent: true,
        isPublic: true,
        isAvailable: true,
        phoneNumber: genRandomPhoneNumber(),
      })
      .where(eq(usersTable.id, userId));

    // User address
    const [userAddress] = await tx
      .insert(addressTable)
      .values({ city: userCity, state: userState })
      .returning({ userAddressId: addressTable.id });

    const { userAddressId } = userAddress ?? {};

    if (!userAddressId) {
      throw new Error('❌ FAILED TO CREATE ADDRESS');
    }

    await tx.insert(userAddressTable).values({
      addressId: userAddressId,
      userId,
    });

    // Social links
    const socialLinkCount = genRandomNumberBetween(1, 3);
    await tx.insert(socialLinkTable).values(
      Array.from({ length: socialLinkCount }).map((i) => ({
        userId,
        url: genRandomSocialUrl(`${userUsername}_${i}`),
      }))
    );

    // Organization
    const [organization] = await tx
      .insert(organizationTable)
      .values({
        logo: orgLogo,
        name: orgName,
        description: orgDescription,
        ein: genRandomNumberOfLen(9),
        cageCode: genRandomNumberOfLen(4),
        primaryNaicsCode: genRandomNumberOfLen(6),
      })
      .returning({ organizationId: organizationTable.id });

    const { organizationId } = organization ?? {};

    if (!organizationId) {
      throw new Error('❌ FAILED TO CREATE ORGANIZATION');
    }

    // Membership
    await tx.insert(membershipTable).values({
      userId,
      organizationId,
      roleId: MembershipRole.Owner,
    });

    // Organization address
    const [orgAddress] = await tx
      .insert(addressTable)
      .values({
        address1: orgAddress1,
        city: orgCity,
        state: orgState,
        zipCode: genRandomNumberOfLen(5),
      })
      .returning({ orgAddressId: addressTable.id });

    const { orgAddressId } = orgAddress ?? {};

    if (!orgAddressId) {
      throw new Error('❌ FAILED TO CREATE ADDRESS');
    }

    await tx.insert(organizationAddressTable).values({
      addressId: orgAddressId,
      organizationId,
    });

    return userId;
  });
}

// -----------------------------------------------------------------------------------
// Utility Functions
// -----------------------------------------------------------------------------------
// GENERATE RANDOM NUMBER BETWEEN SPECIFIED RANGE
export function genRandomNumberBetween(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// GENERATE RANDOM NUMBER OF SPECIFIED LENGTH
export function genRandomNumberOfLen(len: number): string {
  return String(Math.floor(Math.random() * Math.pow(10, len))).padStart(
    len,
    '0'
  );
}

// GENERATE RANDOM TIMESTAMP
export function genRandomTimestamp(): string {
  const now = new Date();
  const randomDays = Math.random() * 365;
  return new Date(
    now.getTime() - randomDays * 24 * 60 * 60 * 1000
  ).toISOString();
}

// GENERATE RANDOM HASH
export function genRandomHash(): string {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
}

// GENERATE RANDOM VALID US PHONE NUMBERS
export function genRandomPhoneNumber(): string {
  const areaCode = genRandomNumberBetween(201, 989);
  const subscriberNumber = genRandomNumberOfLen(7);
  return `+1${areaCode}${subscriberNumber}`;
}

// GENERATE RANDOM BOOLEAN
export function genRandomBoolean(): boolean {
  return Math.random() < 0.5;
}

// GENERATE RANDOM URL
export function genRandomUrl(urlPath = ''): string {
  const domains = ['example.com', 'test.com', 'demo.com'];
  const randomDomain = domains[Math.floor(Math.random() * domains.length)];
  return `https://www.${randomDomain}/${urlPath}`;
}

// GENERATE RANDOM SOCIAL URL
export function genRandomSocialUrl(urlPath = ''): string {
  const domains = [
    'linkedin.com',
    'x.com',
    'facebook.com',
    'youtube.com',
    'github.com',
    'stackoverflow.com',
    'medium.com',
    'dev.to',
  ];
  const randomDomain = domains[Math.floor(Math.random() * domains.length)];
  return `https://www.${randomDomain}/${urlPath}`;
}

// GET RANDOM OPTION FROM ARRAY
export function getRandomOption(options: string[]): string {
  return options[genRandomNumberBetween(0, options.length - 1)] as string;
}
