import type * as enums from './enums';
import type * as tables from './tables';

// Enums

export type RateTypeEnumType = (typeof enums.rateTypes)[number];

export type LocationTypeEnumType = (typeof enums.locationTypes)[number];

export type EmploymentTypeEnumType = (typeof enums.employmentTypes)[number];

export type ExpertiseLevelEnumType = (typeof enums.expertiseLevels)[number];

export type RelationshipStrengthTypeEnumType =
  (typeof enums.relationshipStrengthTypes)[number];

export type InvitationStatusTypeEnumType =
  (typeof enums.invitationStatusTypes)[number];

export type FeedbackTypeEnumType = (typeof enums.feedbackTypes)[number];

export type NotificationChannelTypeEnumType =
  (typeof enums.notificationChannelTypes)[number];

export type UserReviewStatusTypeEnumType =
  (typeof enums.userReviewStatusTypes)[number];

export type AttachmentMetadata = {
  id: number | null;
  name: string | null;
  type: string | null;
  size: number | null;
  url: string | null;
};

export type EmojiMetadata = {
  user_id: string;
  content: string;
};

export type ReportedMessageStatusEnumType =
  (typeof enums.reportedMessageStatuses)[number];

export type OrganizationJoinRequestStatusEnumType =
  (typeof enums.organizationJoinRequestStatuses)[number];

export type SubscriptionStatusEnumType =
  (typeof enums.subscriptionStatuses)[number];

export type SubscriptionPlanType = 'BASIC' | 'PREMIUM';

// Tables

export type PayRateRequest = typeof tables.payRateTable.$inferInsert;
export type PayRateResponse = typeof tables.payRateTable.$inferSelect;

export type UsersRequest = Omit<
  typeof tables.usersTable.$inferInsert,
  'id' | 'createdAt' | 'updatedAt'
>;
export type UsersResponse = {
  id: string;
  avatar?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  username?: string | null;
  phoneNumber?: string | null;
  biography?: string | null;
  headline?: string | null;
  securityClearance?: string | null;
  legalStatus?: string | null;
  obEmailSent?: boolean;
  fteAvailable?: boolean;
  isPublic?: boolean;
  isAvailable?: boolean;
  unavailableUntil?: Date | null;
  hasCompletedTour?: boolean;
  payRateId?: number | null;
  createdAt?: Date;
  updatedAt?: Date | null;
};

export interface UserProfilePreferences {
  isPublic?: boolean;
  isAvailable?: boolean;
  unavailableUntil?: Date | null;
}

export interface UserWithAddressResponse extends UsersResponse {
  address?: AddressResponse | null;
}

export type RoleRequest = Omit<
  typeof tables.roleTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type RoleResponse = typeof tables.roleTable.$inferSelect;

export type PermissionRequest = Omit<
  typeof tables.permissionTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type PermissionResponse = typeof tables.permissionTable.$inferSelect;

export type OrganizationRequest = Omit<
  typeof tables.organizationTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type OrganizationResponse = Omit<
  typeof tables.organizationTable.$inferSelect,
  'createdAt' | 'updatedAt'
> & {
  address?: AddressResponse | null;
  subscription?: OrganizationSubscriptionResponse | null;
};

export type MembershipRequest = Omit<
  typeof tables.membershipTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type MembershipResponse = {
  roleId: enums.MembershipRole;
  user: UsersResponse;
  organization: OrganizationResponse;
};

export interface OrganizationMemberResponse {
  membershipId: number;
  role: enums.MembershipRole;
  data: UsersResponse;
  name?: string;
  email?: string | null;
  status: string;
}

export type InvitationRequest = Omit<
  typeof tables.invitationTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type InvitationResponse = Omit<
  typeof tables.invitationTable.$inferSelect,
  'organizationId' | 'inviterId' | 'createdAt' | 'updatedAt'
> & {
  organization: Pick<OrganizationResponse, 'id' | 'name'>;
  inviter: Partial<UsersResponse> | null;
};

export type SubscriptionRequest = typeof tables.subscriptionTable.$inferInsert;
export type SubscriptionResponse = typeof tables.subscriptionTable.$inferSelect;

export type OrganizationSubscriptionRequest =
  typeof tables.organizationSubscriptionTable.$inferInsert;
export type OrganizationSubscriptionResponse = SubscriptionResponse & {
  customerId: string;
};

export type AddressRequest = Omit<
  typeof tables.addressTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type AddressResponse = Omit<
  typeof tables.addressTable.$inferSelect,
  'createdAt' | 'updatedAt'
>;

export interface USState {
  id: number;
  name: string;
  abbreviation: string;
}

export type OrganizationAddressRequest = Omit<
  typeof tables.organizationAddressTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type OrganizationAddressResponse =
  typeof tables.organizationAddressTable.$inferSelect;

export type UserAddressRequest = Omit<
  typeof tables.userAddressTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type UserAddressResponse = typeof tables.userAddressTable.$inferSelect;

export type ExperienceRequest = Omit<
  typeof tables.experienceTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type ExperienceResponse = Omit<
  typeof tables.experienceTable.$inferSelect,
  'createdAt' | 'updatedAt' | 'userId'
> & {
  position: PositionResponse;
  skills: SkillResponse[];
};

export type PositionRequest = Omit<
  typeof tables.positionTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type PositionResponse = Omit<
  typeof tables.positionTable.$inferSelect,
  'createdAt' | 'updatedAt'
>;

export type ExperiencePositionRequest = Omit<
  typeof tables.experiencePositionTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type ExperiencePositionResponse =
  typeof tables.experiencePositionTable.$inferSelect;

export type SkillRequest = Omit<
  typeof tables.skillTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type SkillResponse = Omit<
  typeof tables.skillTable.$inferSelect,
  'createdAt' | 'updatedAt'
>;

export type UserSkillRequest = Omit<
  typeof tables.userSkillTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type UserSkillResponse = {
  id: number;
  skill: SkillResponse;
  expertiseLevel: ExpertiseLevelEnumType;
};

export type ExperienceSkillRequest = Omit<
  typeof tables.experienceSkillTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type ExperienceSkillResponse =
  typeof tables.experienceSkillTable.$inferSelect;

export type GovernmentContractRequest = Omit<
  typeof tables.governmentContractTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type GovernmentContractResponse = Omit<
  typeof tables.governmentContractTable.$inferSelect,
  'createdAt' | 'updatedAt'
>;

export type PastPerformanceRequest = Omit<
  typeof tables.pastPerformanceTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type PastPerformanceResponse =
  typeof tables.pastPerformanceTable.$inferSelect;

export enum GovernmentEntityType {
  DEPARTMENT = 'department',
  AGENCY = 'agency',
  OFFICE = 'office',
  SUBOFFICE = 'suboffice',
}

export interface BaseGovernmentEntityResponse {
  id: number;
  name: string;
}

export interface GovernmentEntityResponse extends BaseGovernmentEntityResponse {
  department?: { name: string } | null;
  agency?: { name: string | null; department?: string | null } | null;
  office?: {
    name: string | null;
    agency: string | null;
    agencyDepartment: string | null;
  } | null;
}

export type UserPastPerformanceRequest = Omit<
  typeof tables.userPastPerformanceTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type UserPastPerformanceResponse = {
  pastPerformanceId: number;
  description: string | null;
  awardDate: string;
  position: PositionResponse;
  governmentContract: GovernmentContractResponse;
  department: BaseGovernmentEntityResponse | null;
  agency: BaseGovernmentEntityResponse | null;
  office: BaseGovernmentEntityResponse | null;
  subOffice: BaseGovernmentEntityResponse | null;
};

export type GovernmentDepartmentRequest =
  typeof tables.governmentDepartmentTable.$inferInsert;
export type GovernmentDepartmentResponse =
  typeof tables.governmentDepartmentTable.$inferSelect;

export type GovernmentAgencyRequest =
  typeof tables.governmentAgencyTable.$inferInsert;
export type GovernmentAgencyResponse =
  typeof tables.governmentAgencyTable.$inferSelect;

export type GovernmentOfficeRequest =
  typeof tables.governmentOfficeTable.$inferInsert;
export type GovernmentOfficeResponse =
  typeof tables.governmentOfficeTable.$inferSelect;

export type GovernmentSubOfficeRequest =
  typeof tables.governmentSubOfficeTable.$inferInsert;
export type GovernmentSubOfficeResponse =
  typeof tables.governmentSubOfficeTable.$inferSelect;

export type DepartmentContractRequest = Omit<
  typeof tables.departmentContractTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type DepartmentContractResponse =
  typeof tables.departmentContractTable.$inferSelect;

export type AgencyContractRequest = Omit<
  typeof tables.agencyContractTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type AgencyContractResponse =
  typeof tables.agencyContractTable.$inferSelect;

export type OfficeContractRequest = Omit<
  typeof tables.officeContractTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type OfficeContractResponse =
  typeof tables.officeContractTable.$inferSelect;

export type SubOfficeContractRequest = Omit<
  typeof tables.subOfficeContractTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type SubOfficeContractResponse =
  typeof tables.subOfficeContractTable.$inferSelect;

export type AgencyInsightRequest = Omit<
  typeof tables.agencyInsightTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type AgencyInsightResponse = {
  id: number;
  description: string | null;
  relationshipStrength: RelationshipStrengthTypeEnumType;
  position: PositionResponse;
  department: BaseGovernmentEntityResponse;
  agency: BaseGovernmentEntityResponse | null;
  office: BaseGovernmentEntityResponse | null;
  subOffice: BaseGovernmentEntityResponse | null;
};

export type SchoolRequest = typeof tables.schoolTable.$inferInsert;
export type SchoolResponse = typeof tables.schoolTable.$inferSelect;
export type SchoolResponseBasic = Pick<
  typeof tables.schoolTable.$inferSelect,
  'id' | 'name'
>;

export type SubjectRequest = typeof tables.subjectTable.$inferInsert;
export type SubjectResponse = typeof tables.subjectTable.$inferSelect;

export type DegreeRequest = typeof tables.degreeTable.$inferInsert;
export type DegreeResponse = typeof tables.degreeTable.$inferSelect;

export type EducationRequest = Omit<
  typeof tables.educationTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type EducationResponse = {
  id: number;
  school: SchoolResponseBasic;
  degrees: UserDegreeResponse[];
};

export type UserDegreeRequest = Omit<
  typeof tables.userDegreeTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type UserDegreeResponse = {
  id: number;
  degree: DegreeResponse;
  majors: SubjectResponse[];
  minors: SubjectResponse[];
  startDate: string;
  endDate: string | null;
  currentlyStudying: boolean | null;
};

export type MajorRequest = Omit<
  typeof tables.majorTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type MajorResponse = typeof tables.majorTable.$inferSelect;

export type MinorRequest = Omit<
  typeof tables.minorTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type MinorResponse = typeof tables.minorTable.$inferSelect;

export type CertificationRequest = Omit<
  typeof tables.certificationTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type CertificationResponse = Omit<
  typeof tables.certificationTable.$inferSelect,
  'createdAt' | 'updatedAt'
>;

export type UserCertificationRequest = Omit<
  typeof tables.userCertificationTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type UserCertificationResponse = {
  id: number;
  credentialId: string | null;
  credentialUrl: string | null;
  issuedDate: string | null;
  expirationDate: string | null;
  certificationName: string | null;
  certificationProvider: string | null;
};

export type ServiceRequest = Omit<
  typeof tables.serviceTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type ServiceResponse = Pick<
  typeof tables.serviceTable.$inferSelect,
  'id' | 'name'
>;

export type UserServiceRequest = Omit<
  typeof tables.userServiceTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type UserServiceResponse = {
  id: number;
  fixedPrice: number | null;
  payRate: Pick<PayRateResponse, 'id' | 'label'> | null;
  expertiseLevel: ExpertiseLevelEnumType;
  service: ServiceResponse;
};

export type FullTimeAvailabilityRequest = Omit<
  typeof tables.fullTimeAvailabilityTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type FullTimeAvailabilityResponse = {
  id: number;
  desiredStartDate: string | null;
  locationType: LocationTypeEnumType | null;
  payRate: Pick<PayRateResponse, 'id' | 'label'>;
  position: PositionResponse;
};

export type FeedbackRequest = Omit<
  typeof tables.feedbackTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type FeedbackResponse = Omit<
  typeof tables.feedbackTable.$inferSelect,
  'userId'
> & {
  user: Partial<UsersResponse>;
};

export type FeedbackFileRequest = Omit<
  typeof tables.feedbackFileTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type FeedbackFileResponse = typeof tables.feedbackFileTable.$inferSelect;

export type SocialLinkRequest = Omit<
  typeof tables.socialLinkTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type SocialLinkResponse = Pick<
  typeof tables.socialLinkTable.$inferSelect,
  'id' | 'url'
>;

export type NotificationRequest = Omit<
  typeof tables.notificationTable.$inferInsert,
  'createdAt'
>;
export type NotificationResponse = typeof tables.notificationTable.$inferSelect;

export type UserNotificationPreferenceRequest = Omit<
  typeof tables.userNotificationPreferenceTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type UserNotificationPreferenceResponse =
  typeof tables.userNotificationPreferenceTable.$inferSelect;

export type UserReviewRequestRequest = Omit<
  typeof tables.userReviewRequestTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type UserReviewRequestResponse = Omit<
  typeof tables.userReviewRequestTable.$inferSelect,
  'updatedAt' | 'requesterId'
> & {
  requester: Partial<UsersResponse>;
};

export type UserReviewRequest = Omit<
  typeof tables.userReviewTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type UserReviewResponse = Omit<
  typeof tables.userReviewTable.$inferSelect,
  'updatedAt' | 'userId' | 'reviewerId' | 'overallRating'
> & {
  overallRating: number;
  user: Partial<UsersResponse>;
  reviewer?: Partial<UsersResponse>;
};

export type ChatRoomRequest = Omit<
  typeof tables.chatRoomTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type ChatRoomResponse = typeof tables.chatRoomTable.$inferSelect;

export type ChatRoomMemberRequest =
  typeof tables.chatRoomMemberTable.$inferInsert;
export type ChatRoomMemberResponse = Omit<
  typeof tables.chatRoomMemberTable.$inferSelect,
  'userId'
> & {
  member: Partial<UsersResponse>;
};

export type MessageRequest = Omit<
  typeof tables.messageTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type MessageResponse = typeof tables.messageTable.$inferSelect;

export type ReportedMessageRequest = Omit<
  typeof tables.reportedMessageTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type ReportedMessageResponse =
  typeof tables.reportedMessageTable.$inferSelect;

export type ConnectionRequestRequest = Omit<
  typeof tables.connectionRequestTable.$inferInsert,
  'createdAt'
>;
export type ConnectionRequestResponse = Omit<
  typeof tables.connectionRequestTable.$inferSelect,
  'requesterId' | 'receiverId'
> & {
  requester: Partial<UsersResponse>;
  receiver: Partial<UsersResponse>;
};

export type ConnectionRequest = typeof tables.connectionTable.$inferInsert;
export type ConnectionResponse = typeof tables.connectionTable.$inferSelect;

export type InvoiceRequest = Omit<
  typeof tables.invoiceTable.$inferInsert,
  'createdAt'
>;
export type InvoiceResponse = typeof tables.invoiceTable.$inferSelect;

export type OrganizationJoinRequestRequest = Omit<
  typeof tables.organizationJoinRequestTable.$inferInsert,
  'createdAt' | 'updatedAt'
>;
export type OrganizationJoinRequestResponse = Omit<
  typeof tables.organizationJoinRequestTable.$inferSelect,
  'updatedAt' | 'processedBy'
> & {
  processedBy: Partial<UsersResponse> | null;
};

export interface ConsultantResponse {
  id: string;
  user: {
    email: string;
    avatar?: string | null;
    first_name: string;
    last_name?: string | null;
    username?: string | null;
    phone_number: string;
    is_public: boolean;
    is_available: boolean;
    unavailable_until?: string | null;
    pay_rate_label: string;
    security_clearance?: string | null;
    legal_status?: string | null;
    created_at: string;
  };
  user_address: {
    city: string;
    state: string;
  };
  organization: {
    name: string;
    description?: string | null;
    logo?: string | null;
    ein?: string | null;
    primary_naics_code?: string | null;
    cage_code?: string | null;
  };
  experiences: {
    location_type: string;
    employment_type: string;
    start_date: string;
    end_date?: string | null;
    description?: string | null;
    city: string;
    state: string;
    is_current_role?: boolean | null;
    company_name: string;
    position_name: string;
    skill_names?: string[] | null;
  }[];
  past_performances: {
    award_date: string;
    description: string;
    position_name: string;
    contract_name: string;
    contract_description?: string | null;
    contract_rfp_number: string;
    contract_value: number;
    contract_department_name?: string | null;
    contract_agency_name?: string | null;
    contract_office_name?: string | null;
    contract_sub_office_name?: string | null;
  }[];
  agency_insights: {
    position_name: string;
    description?: string | null;
    relationship_strength: string;
    gov_department_name?: string | null;
    gov_agency_name?: string | null;
    gov_office_name?: string | null;
    gov_sub_office_name?: string | null;
  }[];
  educations: {
    school_name: string;
    degrees: {
      degree_name: string;
      start_date: string;
      end_date?: string | null;
      currently_studying?: boolean | null;
      major_names: string[];
      minor_names: string[];
    }[];
  }[];
  expertise: {
    skill_name: string;
    expertise_level: string;
  }[];
  certifications: {
    credential_id?: string | null;
    credential_url?: string | null;
    issue_date?: string | null;
    expiration_date?: string | null;
    name?: string | null;
    provider?: string | null;
  }[];
  services: {
    service_name: string;
    expertise_level: string;
    fixed_price?: number | null;
    pay_rate_label?: string | null;
  }[];
  full_time_roles: {
    position_name: string;
    pay_rate_label: string;
    location_type: string;
    desired_start_date?: string | null;
  }[];
}

export interface ConsultantProfileCompletion {
  userId: string;
  basicInfo: boolean;
  profilePicture: boolean;
  workExperience: boolean;
  pastPerformance: boolean;
  education: boolean;
  expertise: boolean;
  services: boolean;
  isComplete: boolean;
}
