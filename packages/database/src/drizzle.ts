import type { PgDatabase } from 'drizzle-orm/pg-core';

import { sql } from 'drizzle-orm';
import { type JwtPayload, jwtDecode } from 'jwt-decode';

export function createDrizzle<
  // biome-ignore lint/suspicious/noExplicitAny: No other way to do this
  Database extends PgDatabase<any, any, any>,
>(accessToken: string, client: Database) {
  const token = decode(accessToken);
  const sub = token.sub ? `'${token.sub}'` : 'null';

  const authJwt = sql.raw(`'${JSON.stringify(token)}'`);
  const authUid = sql.raw(`${sub}`);
  const authEmail = sql.raw(`'${token.email}'`);
  const authRole = sql.raw(token.role ?? 'anon');

  return {
    transaction: (async (transaction, ...rest) => {
      return await client.transaction(
        async (tx) => {
          try {
            await tx.execute(sql`
            select set_config('request.jwt.claims', ${authJwt}, true);
            select set_config('request.jwt.claim.sub', ${authUid}, true);
            select set_config('request.jwt.claim.email', ${authEmail}, true);
            set local role ${authRole};
          `);

            return await transaction(tx);
          } finally {
            await tx.execute(sql`
            select set_config('request.jwt.claims', null, true);
            select set_config('request.jwt.claim.sub', null, true);
            select set_config('request.jwt.claim.email', null, true);
            reset role;
          `);
          }
        },
        ...rest
      );
    }) as typeof client.transaction,
  };
}

type SupabaseToken = JwtPayload & {
  email?: string;
  role: string;
};

function decode(accessToken: string) {
  try {
    return jwtDecode<SupabaseToken>(accessToken);
  } catch (error) {
    return { role: 'anon', email: '' } as SupabaseToken;
  }
}
