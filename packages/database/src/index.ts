import type { DrizzleConfig } from 'drizzle-orm';

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

import { getSupabaseServerClient } from '@packages/supabase/server';

import { createDrizzle } from './drizzle';
import * as schema from './schema';

const config = {
  casing: 'snake_case',
  schema,
} satisfies DrizzleConfig<typeof schema>;

// Protected by RLS
const dbClient = drizzle({
  client: postgres(process.env.DATABASE_URL!, { prepare: false }),
  ...config,
});

// ByPass RLS
export const dbAdmin = drizzle({
  client: postgres(process.env.ADMIN_DATABASE_URL!, { prepare: false }),
  ...config,
});

export async function createDrizzleSupabaseClient(params = { admin: false }) {
  if (params.admin) {
    return dbAdmin;
  }

  const supabase = await getSupabaseServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();

  const token = session?.access_token ?? '';
  return createDrizzle(token, dbClient);
}

export type DrizzleSupabaseClient = Awaited<
  ReturnType<typeof createDrizzleSupabaseClient>
>;
export type TransactionType = Parameters<
  Parameters<DrizzleSupabaseClient['transaction']>[0]
>[0];
