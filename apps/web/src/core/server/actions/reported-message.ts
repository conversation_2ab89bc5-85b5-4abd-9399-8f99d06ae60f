'use server';

import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { authActionClient } from '@/core/server/actions/safe-action';
import { createDrizzleSupabaseClient } from '@packages/database';
import { reportedMessageTable } from '@packages/database/tables';

const reportMessageSchema = z.object({
  messageId: z.number(),
});

export const reportMessageAction = authActionClient
  .schema(reportMessageSchema)
  .metadata({ name: 'report-message' })
  .action(async ({ ctx: { authUser }, parsedInput: { messageId } }) => {
    const reporterId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .insert(reportedMessageTable)
        .values({
          messageId,
          reporterId,
        })
        .onConflictDoNothing();
    });

    logger.info('✅ SUCCESSFULLY REPORTED MESSAGE');
  });
