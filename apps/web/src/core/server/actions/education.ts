'use server';

import { z } from 'zod';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { createEducation, updateEducation } from '@packages/database/functions';
import { educationTable } from '@packages/database/tables';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { logger } from '@packages/utils/logger';

import { educationFormSchema } from '@/core/schemas/education';
import { authActionClient } from '@/core/server/actions/safe-action';

export const createEducationAction = authActionClient
  .schema(educationFormSchema)
  .metadata({ name: 'create-education' })
  .action(async ({ ctx: { authUser }, parsedInput: { schoolId, degrees } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const educationId = await createEducation(db, {
      userId,
      schoolId,
      degrees: degrees.map((degree) => ({
        ...degree,
        startDate: degree.startDate.toISOString(),
        endDate: degree.endDate?.toISOString(),
      })),
    });

    logger.info(
      { userId, educationId },
      '✅ SUCCESSFULLY CREATED USER EDUCATION'
    );

    await syncTypesenseWithSupabase(userId);
  });

const updateEducationFormSchema = educationFormSchema.extend({
  educationId: z.number(),
});

export const updateEducationAction = authActionClient
  .schema(updateEducationFormSchema)
  .metadata({ name: 'update-education' })
  .action(async ({ ctx: { authUser }, parsedInput: { degrees, ...data } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const educationId = await updateEducation(db, {
      userId,
      ...data,
      degrees: degrees.map((degree) => ({
        ...degree,
        startDate: degree.startDate.toISOString(),
        endDate: degree.endDate?.toISOString(),
      })),
    });

    logger.info(
      { userId, educationId },
      '✅ SUCCESSFULLY UPDATED USER EDUCATION'
    );

    await syncTypesenseWithSupabase(userId);
  });

const deleteEducationFormSchema = z.object({
  id: z.number(),
});

export const deleteEducationAction = authActionClient
  .schema(deleteEducationFormSchema)
  .metadata({ name: 'delete-education' })
  .action(async ({ ctx: { authUser }, parsedInput: { id } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx.delete(educationTable).where(eq(educationTable.id, id));
    });

    logger.info({ educationId: id }, '✅ SUCCESSFULLY DELETED USER EDUCATION');

    await syncTypesenseWithSupabase(userId);

    return id;
  });
