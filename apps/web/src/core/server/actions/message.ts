'use server';

import type { AttachmentMetadata } from '@packages/database/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { sendInitialMessage, sendMessage } from '@packages/database/functions';
import { uploadAttachments } from '@packages/supabase/storage/attachment';
import { logger } from '@packages/utils/logger';
import { redirect } from 'next/navigation';

import { SiteConfig } from '@/configuration';
import {
  initialMessageFormSchema,
  messageFormSchema,
  updateEmojiSchema,
} from '@/core/schemas/message';
import { authActionClient } from '@/core/server/actions/safe-action';
import { and, eq } from '@packages/database/drizzle-orm';
import { messageTable } from '@packages/database/tables';

export const sendMessageAction = authActionClient
  .schema(messageFormSchema)
  .metadata({ name: 'send-message' })
  .action(async ({ ctx: { authUser }, parsedInput: data }) => {
    const senderId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const messageId = await sendMessage(db, {
      senderId,
      ...data,
    });

    logger.info({ senderId, messageId }, '✅ SUCCESSFULLY SENT MESSAGE');
  });

export const sendInitialMessageAction = authActionClient
  .schema(initialMessageFormSchema)
  .metadata({ name: 'send-initial-message' })
  .action(
    async ({
      ctx: { supabase, authUser },
      parsedInput: { attachments, ...data },
    }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      let formattedAttachments: AttachmentMetadata[] = [];

      try {
        formattedAttachments = await uploadAttachments({
          attachments: attachments || [],
          roomId: data.roomId,
          supabase,
        });

        logger.info(
          { formattedAttachments },
          '✅ SUCCESSFULLY UPLOADED ATTACHMENTS'
        );
      } catch (error) {
        logger.error({ error, userId }, '❌ ERROR UPLOADING ATTACHMENTS');
        throw error;
      }

      const response = await sendInitialMessage(db, {
        attachments: formattedAttachments,
        senderId: userId,
        ...data,
      });

      logger.info({ userId, ...response }, '✅ SUCCESSFULLY SENT MESSAGE');

      redirect(`${SiteConfig.paths.app.messages}/${response.roomId}`);
    }
  );

export const updateEmojiAction = authActionClient
  .schema(updateEmojiSchema)
  .metadata({ name: 'update-emoji' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const dbAdmin = await createDrizzleSupabaseClient({ admin: true });

    await dbAdmin.transaction(async (tx) => {
      await tx
        .update(messageTable)
        .set({
          emojis: parsedInput.emojis,
        })
        .where(
          and(
            eq(messageTable.ablyTimeserial, parsedInput.ablyTimeserial),
            eq(messageTable.roomId, parsedInput.roomId)
          )
        );
    });

    logger.info({ userId }, '✅ SUCCESSFULLY UPDATED EMOJIS');
  });
