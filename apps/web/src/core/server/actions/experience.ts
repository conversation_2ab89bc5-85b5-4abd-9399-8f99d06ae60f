'use server';

import { z } from 'zod';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  createWorkExperience,
  updateWorkExperience,
} from '@packages/database/functions';
import { experienceTable } from '@packages/database/tables';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { logger } from '@packages/utils/logger';

import { experienceFormSchema } from '@/core/schemas/experience';
import { authActionClient } from '@/core/server/actions/safe-action';

export const createExperienceAction = authActionClient
  .schema(experienceFormSchema)
  .metadata({ name: 'create-experience' })
  .action(
    async ({
      ctx: { authUser },
      parsedInput: { position, startDate, endDate, ...data },
    }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      const experienceId = await createWorkExperience(db, {
        userId,
        ...data,
        positionId: position.id,
        positionName: position.name,
        startDate: startDate.toISOString(),
        endDate: endDate?.toISOString(),
      });

      logger.info(
        { userId, experienceId },
        '✅ SUCCESSFULLY CREATED USER WORK EXPERIENCE'
      );

      await syncTypesenseWithSupabase(userId);
    }
  );

const updateExperienceActionFormSchema = experienceFormSchema.extend({
  experienceId: z.number(),
});

export const updateExperienceAction = authActionClient
  .schema(updateExperienceActionFormSchema)
  .metadata({ name: 'update-experience' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const { experienceId, position, startDate, endDate, ...data } = parsedInput;

    await updateWorkExperience(db, {
      userId,
      ...data,
      experienceId,
      positionId: position.id,
      positionName: position.name,
      startDate: startDate.toISOString(),
      endDate: endDate?.toISOString(),
    });

    logger.info(
      { userId, experienceId },
      '✅ SUCCESSFULLY UPDATED USER WORK EXPERIENCE'
    );

    await syncTypesenseWithSupabase(userId);
  });

const deleteExperienceSchema = z.object({
  id: z.number(),
});

export const deleteExperienceAction = authActionClient
  .schema(deleteExperienceSchema)
  .metadata({ name: 'delete-experience' })
  .action(async ({ ctx: { authUser }, parsedInput: { id } }) => {
    const userId = authUser.id;

    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx.delete(experienceTable).where(eq(experienceTable.id, id));
    });

    logger.info(
      { experienceId: id },
      '✅ SUCCESSFULLY DELETED USER WORK EXPERIENCE'
    );

    await syncTypesenseWithSupabase(userId);

    return id;
  });
