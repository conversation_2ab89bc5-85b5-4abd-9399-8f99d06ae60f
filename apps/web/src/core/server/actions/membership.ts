'use server';

import { MembershipRole } from '@packages/database/enums';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { z } from 'zod';

import { SiteConfig } from '@/configuration';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { acceptInviteToOrganization } from '@packages/database/functions';
import { membershipTable } from '@packages/database/tables';
import { getSupabaseServerClient } from '@packages/supabase/server';
import { logger } from '@packages/utils/logger';

import { authActionClient } from '@/core/server/actions/safe-action';

const updateMemberSchema = z.object({
  role: z.nativeEnum(MembershipRole),
  membershipId: z.number(),
});

export const updateMemberAction = authActionClient
  .schema(updateMemberSchema)
  .metadata({ name: 'update-member' })
  .action(
    async ({ ctx: { authUser }, parsedInput: { membershipId, role } }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      await db.transaction(async (tx) => {
        await tx
          .update(membershipTable)
          .set({
            roleId: role,
            userId,
          })
          .where(eq(membershipTable.id, membershipId));
      });

      logger.info({ membershipId }, 'Member successfully updated.');

      revalidatePath('/settings/organization/members');

      return { success: true };
    }
  );

export const deleteMemberAction = authActionClient
  .schema(z.object({ membershipId: z.number() }))
  .metadata({ name: 'delete-member' })
  .action(async ({ parsedInput: { membershipId } }) => {
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .delete(membershipTable)
        .where(eq(membershipTable.id, membershipId));
    });

    logger.info({ membershipId }, 'Member successfully deleted.');

    return {
      success: true,
    };
  });

// Accept Invite Action
export const acceptInviteAction = async (params: {
  code: string;
  userId?: string;
  invitationEmail: string;
  firstName?: string;
  lastName?: string;
}) => {
  const { code, firstName, lastName } = params;

  const dbAdmin = await createDrizzleSupabaseClient({ admin: true });
  const supabaseAdmin = await getSupabaseServerClient({ admin: true });

  // if the user ID is provided, we use it
  // (for example, when signing up for the 1st time)
  let email = '';
  let userId = params.userId;
  let signedIn = false;

  // if the user ID is not provided, we try to get it from the session
  if (!userId) {
    const supabase = await getSupabaseServerClient();
    const { data: authUser } = await supabase.auth.getUser();

    // if the session is not available, we throw an error
    if (!authUser.user) {
      throw new Error('Session not available');
    }

    email = String(authUser.user.email);
    userId = authUser.user.id;
    signedIn = true;
  }
  if (!!userId) {
    const { data: authUser, error } =
      await supabaseAdmin.auth.admin.getUserById(userId);
    if (error) {
      throw new Error(`Error getting user: ${error.message}`);
    }
    email = String(authUser.user.email);
  }

  if (email !== params.invitationEmail) {
    throw new Error('Email does not match invitation email');
  }

  logger.info({ code, userId }, 'Adding member to organization...');

  const { organizationId, membershipId } = await acceptInviteToOrganization(
    dbAdmin,
    {
      inviteCode: code,
      invitedUserId: userId,
      invitedUserEmail: email,
      invitedUserFirstName: firstName,
      invitedUserLastName: lastName,
    }
  );

  logger.info(
    { membershipId, organizationId, userId },
    'Member successfully added to organization'
  );

  const needsEmailVerification = !signedIn;

  // if the user is *not* required to confirm their email
  // we redirect them to the app home
  if (!needsEmailVerification) {
    logger.info({ membershipId }, 'Redirecting user to app home...');
    redirect(SiteConfig.paths.app.dashboard);
  }

  logger.info(
    { membershipId },
    'User needs to verify their email address - returning JSON response...'
  );

  redirect(SiteConfig.paths.onboarding);
};
