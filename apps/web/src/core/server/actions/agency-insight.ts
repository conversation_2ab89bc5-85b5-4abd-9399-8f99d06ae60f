'use server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { createAgencyInsight } from '@packages/database/functions';
import { updateAgencyInsight } from '@packages/database/functions';
import { agencyInsightTable } from '@packages/database/tables';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { baseAgencyInsightFormSchema } from '@/core/schemas/agency-insight';
import { agencyInsightFormSchema } from '@/core/schemas/agency-insight';
import { authActionClient } from '@/core/server/actions/safe-action';

export const createAgencyInsightAction = authActionClient
  .schema(agencyInsightFormSchema)
  .metadata({ name: 'create-agency-insight' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const { position, department, agency, office, subOffice, ...data } =
      parsedInput;

    const db = await createDrizzleSupabaseClient();

    const agencyInsightId = await createAgencyInsight(db, {
      userId,
      officeId: office.id,
      agencyId: agency.id,
      subOfficeId: subOffice.id,
      positionId: position.id,
      positionName: position.name,
      departmentId: department.id,
      ...data,
    });

    logger.info({ agencyInsightId }, '✅ SUCCESSFULLY CREATED AGENCY INSIGHT');

    await syncTypesenseWithSupabase(userId);
  });

const updateAgencyInsightFormSchema = baseAgencyInsightFormSchema.extend({
  id: z.number(),
});

export const updateAgencyInsightAction = authActionClient
  .schema(updateAgencyInsightFormSchema)
  .metadata({ name: 'update-agency-insight' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const { id, position, department, agency, office, subOffice, ...data } =
      parsedInput;

    const db = await createDrizzleSupabaseClient();

    await updateAgencyInsight(db, {
      userId,
      positionId: position.id,
      positionName: position.name,
      agencyInsightId: id,
      departmentId: department.id,
      agencyId: agency.id,
      officeId: office.id,
      subOfficeId: subOffice.id,
      ...data,
    });

    logger.info(
      { agencyInsightId: id },
      '✅ SUCCESSFULLY UPDATED AGENCY INSIGHT'
    );

    await syncTypesenseWithSupabase(userId);
  });

const deleteAgencyInsightFormSchema = z.object({
  id: z.number(),
});

export const deleteAgencyInsightAction = authActionClient
  .schema(deleteAgencyInsightFormSchema)
  .metadata({ name: 'delete-agency-insight' })
  .action(async ({ ctx: { authUser }, parsedInput: { id } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx.delete(agencyInsightTable).where(eq(agencyInsightTable.id, id));
    });

    logger.info(
      { agencyInsightId: id },
      '✅ SUCCESSFULLY DELETED AGENCY INSIGHT'
    );

    await syncTypesenseWithSupabase(userId);
  });
