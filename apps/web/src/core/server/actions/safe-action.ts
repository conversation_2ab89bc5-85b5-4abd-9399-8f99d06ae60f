import { getRateLimiter } from '@packages/upstash';
import {
  DEFAULT_SERVER_ERROR_MESSAGE,
  createSafeActionClient,
} from 'next-safe-action';
import { z } from 'zod';

import { SiteConfig } from '@/configuration';
import { getAuthUser } from '@packages/supabase/auth-fns/get-auth-user';
import { getSupabaseServerClient } from '@packages/supabase/server';
import { getClientIp } from '@packages/utils/http';
import { logger } from '@packages/utils/logger';

export const safeActionClientWithMetadata = createSafeActionClient({
  defaultValidationErrorsShape: 'flattened',
  handleServerError: (error) => {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(DEFAULT_SERVER_ERROR_MESSAGE);
  },
  defineMetadataSchema: () => {
    return z.object({
      name: z.string(),
      rateLimit: z.number().optional(),
    });
  },
});

export const rateLimitedActionClient = safeActionClientWithMetadata.use(
  async ({ next, metadata }) => {
    const response = next({ ctx: {} });

    if (SiteConfig.isProduction) {
      const ip = await getClientIp();
      const identifier = `${ip}-${metadata?.name}`;
      const ratelimit = getRateLimiter(metadata?.rateLimit);

      const { success, remaining } = await ratelimit.limit(identifier);

      if (!success) {
        logger.error({ remaining }, 'Rate limit exceeded');
        throw new Error('Too many requests');
      }
      return response;
    }

    return response;
  }
);

export const authActionClient = rateLimitedActionClient.use(
  async ({ next }) => {
    const supabase = await getSupabaseServerClient();
    const { authUser, error } = await getAuthUser(supabase);

    if (error || !authUser) {
      throw new Error('Unauthorized');
    }

    return next({
      ctx: {
        supabase,
        authUser,
      },
    });
  }
);
