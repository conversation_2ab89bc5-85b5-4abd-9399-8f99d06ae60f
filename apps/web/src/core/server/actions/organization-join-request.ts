'use server';

import type { Dr<PERSON>zleSupabaseClient } from '@packages/database';
import { MembershipRole } from '@packages/database/enums';
import type { Supabase } from '@packages/supabase/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq, inArray } from '@packages/database/drizzle-orm';
import {
  approveOrganizationJoinRequests,
  getOrganizationAdminMembershipsBasic,
} from '@packages/database/functions';
import {
  membershipTable,
  organizationJoinRequestTable,
  organizationTable,
} from '@packages/database/tables';
import {
  renderOrganizationJoinRequestApprovedEmail,
  renderOrganizationJoinRequestEmail,
  renderOrganizationJoinRequestRejectedEmail,
} from '@packages/email';
import { sendEmail } from '@packages/mailer';

import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { SiteConfig } from '@/configuration';
import { authActionClient } from '@/core/server/actions/safe-action';
import { getNotificationsApi } from '@/lib/notifications/server/api';
import { NotificationType } from '@/lib/notifications/types';

const createOrganizationJoinRequestSchema = z.object({
  organizationId: z.string().min(1),
});

export const createOrganizationJoinRequestAction = authActionClient
  .schema(createOrganizationJoinRequestSchema)
  .metadata({ name: 'create-organization-join-request' })
  .action(async ({ ctx: { authUser }, parsedInput: { organizationId } }) => {
    const userId = authUser.id;
    const userEmail = authUser.email!;
    const db = await createDrizzleSupabaseClient();
    const dbAdmin = await createDrizzleSupabaseClient({ admin: true });

    await db.transaction(async (tx) => {
      await tx.insert(organizationJoinRequestTable).values({
        email: userEmail,
        organizationId,
        userId,
      });
    });

    logger.info('✅ SUCCESSFULLY CREATED ORGANIZATION JOIN REQUEST');

    await sendOrgJoinRequestNotificationsAndEmails({
      dbAdmin,
      organizationId,
      requesterId: userId,
      requesterEmail: userEmail,
    });
  });

const approveOrganizationJoinRequestsSchema = z.object({
  orgJoinRequestIds: z.array(z.number().min(1)),
  organizationId: z.string().min(1),
  roleId: z.nativeEnum(MembershipRole),
});

export const approveOrganizationJoinRequestsAction = authActionClient
  .schema(approveOrganizationJoinRequestsSchema)
  .metadata({ name: 'approve-organization-join-requests' })
  .action(
    async ({
      ctx: { authUser },
      parsedInput: { orgJoinRequestIds, organizationId, roleId },
    }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();
      const dbAdmin = await createDrizzleSupabaseClient({ admin: true });

      const approvedUsersEmails = await approveOrganizationJoinRequests(
        dbAdmin,
        {
          orgJoinRequestIds,
          processedByUserId: userId,
          organizationId,
          roleId,
        }
      );

      if (!approvedUsersEmails.length) {
        logger.error(
          { userId },
          '❌ ERROR APPROVING ORGANIZATION JOIN REQUESTS'
        );
        throw new Error('ERROR APPROVING ORGANIZATION JOIN REQUESTS');
      }

      logger.info(
        { approvedUsersEmails },
        '✅ SUCCESSFULLY APPROVED ORGANIZATION JOIN REQUESTS'
      );

      const organizationName = await getOrganizationNameByUserId(db, userId);

      await sendOrgJoinRequestApprovedEmails(
        approvedUsersEmails,
        organizationName
      );
    }
  );

const rejectOrganizationJoinRequestsSchema = z.object({
  orgJoinRequestIds: z.array(z.number().min(1)),
});

export const rejectOrganizationJoinRequestsAction = authActionClient
  .schema(rejectOrganizationJoinRequestsSchema)
  .metadata({ name: 'reject-organization-join-request' })
  .action(async ({ ctx: { authUser }, parsedInput: { orgJoinRequestIds } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const emails = await db.transaction(async (tx) => {
      return await tx
        .update(organizationJoinRequestTable)
        .set({
          status: 'REJECTED',
          processedBy: userId,
        })
        .where(inArray(organizationJoinRequestTable.id, orgJoinRequestIds))
        .returning({ email: organizationJoinRequestTable.email })
        .then((res) => res.map((item) => item.email));
    });

    logger.info(
      { emails },
      '✅ SUCCESSFULLY REJECTED ORGANIZATION JOIN REQUEST'
    );

    const organizationName = await getOrganizationNameByUserId(db, userId);

    await sendOrgJoinRequestRejectedEmails(emails, organizationName);
  });

// -------------------------------------------------------------------------------
//                              UTILITY FUNCTIONS
// -------------------------------------------------------------------------------

async function sendOrgJoinRequestNotificationsAndEmails(params: {
  dbAdmin: DrizzleSupabaseClient;
  organizationId: string;
  requesterId: string;
  requesterEmail: string;
}) {
  const { dbAdmin, organizationId, requesterId, requesterEmail } = params;

  logger.info({ organizationId }, 'ORGANIZATION ID: ');

  const receiversMemberships = await getOrganizationAdminMembershipsBasic(
    dbAdmin,
    { organizationId }
  );

  logger.info({ receiversMemberships }, 'RECEIVERS MEMBERSHIPS: ');

  const receivers = receiversMemberships.map((membership) => membership.user);
  const organizationName = receiversMemberships.at(0)?.organization.name!;

  const notificationApi = getNotificationsApi(dbAdmin);

  const notificationPromises = receivers.map((receiver) =>
    notificationApi.sendNotification({
      userId: receiver.id!,
      type: NotificationType.OrganizationJoinRequest,
      content: `${requesterEmail} has requested to join ${organizationName}`,
      metadata: {
        senderId: requesterId,
        senderEmail: requesterEmail,
        organizationName,
      },
    })
  );

  try {
    logger.info('🚀 SENDING IN-APP NOTIFICATIONS...');
    await Promise.all(notificationPromises);
    logger.info('✅ SUCCESSFULLY SENT IN-APP NOTIFICATIONS');
  } catch (error) {
    logger.error({ error }, '❌ ERROR CREATING NOTIFICATIONS');
  }

  const sender = process.env.EMAIL_SENDER;
  if (!sender) {
    throw new Error(
      'Missing email configuration. Please add the following environment variables: EMAIL_SENDER'
    );
  }

  const organizationRequestsPageLink = new URL(
    SiteConfig.paths.settings.organizationRequests,
    SiteConfig.site.url
  ).href;

  const emailPromises = receivers.map(async (receiver) => {
    const receiverName = `${receiver.firstName} ${receiver.lastName}`;
    const receiverEmail = receiver.email ?? '';
    const subject = `${requesterEmail} has requested to join ${organizationName}!`;

    const content = await renderOrganizationJoinRequestEmail({
      requesterEmail,
      receiverName,
      receiverEmail,
      organizationName,
      organizationRequestsPageLink,
    });

    return sendEmail({
      contentType: 'html',
      to: receiverEmail,
      from: sender,
      subject,
      content,
    });
  });

  try {
    logger.info('🚀 SENDING EMAILS...');
    await Promise.all(emailPromises);
    logger.info('✅ SUCCESSFULLY SENT EMAILS');
  } catch (error) {
    logger.error({ error }, '❌ ERROR SENDING EMAILS');
  }
}

async function sendOrgJoinRequestApprovedEmails(
  receiverEmails: string[],
  organizationName: string
) {
  const sender = process.env.EMAIL_SENDER;
  if (!sender) {
    throw new Error(
      'Missing email configuration. Please add the following environment variables: EMAIL_SENDER'
    );
  }

  const onboardingPath = SiteConfig.paths.onboarding;
  const onboardingPageLink = new URL(onboardingPath, SiteConfig.site.url).href;

  const emailPromises = receiverEmails.map(async (receiverEmail) => {
    const subject = `Your request to join ${organizationName} has been approved!`;
    const content = await renderOrganizationJoinRequestApprovedEmail({
      receiverEmail: receiverEmail,
      organizationName,
      onboardingPageLink,
    });

    return sendEmail({
      contentType: 'html',
      to: receiverEmail,
      from: sender,
      subject,
      content,
    });
  });

  try {
    logger.info('🚀 SENDING EMAILS...');
    await Promise.all(emailPromises);
    logger.info('✅ SUCCESSFULLY SENT EMAILS');
  } catch (error) {
    logger.error({ error }, '❌ ERROR SENDING EMAILS');
  }
}

async function getOrganizationNameByUserId(
  dbAdmin: DrizzleSupabaseClient,
  userId: string
) {
  const [membership] = await dbAdmin.transaction(async (tx) => {
    return tx
      .select({ organizationName: organizationTable.name })
      .from(membershipTable)
      .innerJoin(
        organizationTable,
        eq(membershipTable.organizationId, organizationTable.id)
      )
      .where(eq(membershipTable.userId, userId))
      .limit(1);
  });
  const { organizationName } = membership ?? {};
  if (!organizationName) {
    throw new Error('Organization not found');
  }
  return organizationName;
}

async function sendOrgJoinRequestRejectedEmails(
  receiverEmails: string[],
  organizationName: string
) {
  const sender = process.env.EMAIL_SENDER;
  if (!sender) {
    throw new Error(
      'Missing email configuration. Please add the following environment variables: EMAIL_SENDER'
    );
  }

  const emailPromises = receiverEmails.map(async (receiverEmail) => {
    const subject = `Update on Your Request to Join ${organizationName}`;
    const content = await renderOrganizationJoinRequestRejectedEmail({
      receiverEmail: receiverEmail,
      organizationName,
    });

    return sendEmail({
      contentType: 'html',
      to: receiverEmail,
      from: sender,
      subject,
      content,
    });
  });

  try {
    logger.info('🚀 SENDING EMAILS...');
    await Promise.all(emailPromises);
    logger.info('✅ SUCCESSFULLY SENT EMAILS');
  } catch (error) {
    logger.error({ error }, '❌ ERROR SENDING EMAILS');
  }
}
