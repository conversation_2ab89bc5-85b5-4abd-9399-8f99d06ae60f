'use server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { usersTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { authActionClient } from '@/core/server/actions/safe-action';

const updateUserTourCompletionSchema = z.object({
  completed: z.boolean(),
});

export const updateUserTourCompletionAction = authActionClient
  .schema(updateUserTourCompletionSchema)
  .metadata({ name: 'update-user-tour-completion' })
  .action(async ({ parsedInput: data, ctx: { authUser } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .update(usersTable)
        .set({ hasCompletedTour: data.completed })
        .where(eq(usersTable.id, userId));
    });

    logger.info({ userId }, '✅ SUCCESSFULLY UPDATED USER TOUR COMPLETION');
  });
