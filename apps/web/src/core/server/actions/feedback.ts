'use server';

import { z } from 'zod';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { insertFeedback } from '@packages/database/functions';
import { feedbackTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import {
  bugReportFormSchema,
  consultantRequestFormSchema,
  featureRequestFormSchema,
  feedbackFormSchema,
} from '@/core/schemas/feedback';
import { authActionClient } from '@/core/server/actions/safe-action';

export const createFeedbackAction = authActionClient
  .schema(feedbackFormSchema)
  .metadata({ name: 'create-feedback' })
  .action(
    async ({ ctx: { authUser }, parsedInput: { rating, description } }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      await db.transaction(async (tx) => {
        await tx.insert(feedbackTable).values({
          userId,
          description,
          type: 'FEEDBACK',
          rating: Number.parseInt(rating),
        });
      });

      logger.info({ userId }, '✅ SUCCESSFULLY CREATED FEEDBACK');
    }
  );

export const createBugReportAction = authActionClient
  .schema(bugReportFormSchema)
  .metadata({ name: 'create-bug-report' })
  .action(
    async ({
      ctx: { authUser },
      parsedInput: { issueType, description, fileUrls },
    }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      await insertFeedback(db, {
        userId,
        type: 'BUG',
        description,
        issueType,
        fileUrls,
      });

      logger.info({ userId }, '✅ SUCCESSFULLY CREATED BUG REPORT');
    }
  );

export const createFeatureRequestAction = authActionClient
  .schema(featureRequestFormSchema)
  .metadata({ name: 'create-feature-request' })
  .action(
    async ({
      ctx: { authUser },
      parsedInput: { title, description, fileUrls },
    }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      await insertFeedback(db, {
        userId,
        type: 'FEATURE',
        title,
        description,
        fileUrls,
      });

      logger.info({ userId }, '✅ SUCCESSFULLY CREATED FEATURE REQUEST');
    }
  );

export const createConsultantRequestAction = authActionClient
  .schema(consultantRequestFormSchema)
  .metadata({ name: 'create-consultant-request' })
  .action(
    async ({ ctx: { authUser }, parsedInput: { industries, description } }) => {
      const userId = authUser.id;
      const isClient = authUser.app_metadata?.isClient === true;

      if (!isClient) {
        logger.error(
          { userId },
          '❌ CONSULTANT REQUESTS CAN ONLY BE CREATED BY CLIENTS'
        );
        throw new Error('Consultant requests can only be created by clients');
      }

      const db = await createDrizzleSupabaseClient();

      await db.transaction(async (tx) => {
        await tx.insert(feedbackTable).values({
          userId,
          description,
          title: industries?.join(', '),
          type: 'REQUEST_CONSULTANT',
        });
      });

      logger.info({ userId }, '✅ SUCCESSFULLY CREATED CONSULTANT REQUEST');
    }
  );

const deleteFeedbackSchema = z.object({
  id: z.number(),
});

export const deleteFeedbackAction = authActionClient
  .schema(deleteFeedbackSchema)
  .metadata({ name: 'delete-feedback' })
  .action(async ({ ctx: { authUser }, parsedInput: { id } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx.delete(feedbackTable).where(eq(feedbackTable.id, id));
    });

    logger.info(
      { userId, feedbackId: id },
      '✅ SUCCESSFULLY DELETED USER FEEDBACK'
    );
  });
