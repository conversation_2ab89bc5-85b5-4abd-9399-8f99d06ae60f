'use server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq, inArray } from '@packages/database/drizzle-orm';
import { invitationTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { authActionClient } from '@/core/server/actions/safe-action';

export const deleteInvitationAction = authActionClient
  .schema(z.object({ invitationId: z.number() }))
  .metadata({ name: 'delete-invitation' })
  .action(async ({ ctx: { authUser }, parsedInput: { invitationId } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .delete(invitationTable)
        .where(eq(invitationTable.id, invitationId));
    });

    logger.info({ userId, invitationId }, 'Invitation successfully removed');
  });

export const deleteMultipleInvitationsAction = authActionClient
  .schema(z.object({ invitationIds: z.array(z.number()) }))
  .metadata({ name: 'delete-multiple-invitations' })
  .action(async ({ ctx: { authUser }, parsedInput: { invitationIds } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .delete(invitationTable)
        .where(inArray(invitationTable.id, invitationIds));
    });

    logger.info({ userId, invitationIds }, 'Invitations successfully removed');

    return {
      success: true,
    };
  });
