'use server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { userServiceTable } from '@packages/database/tables';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { serviceFormSchema } from '@/core/schemas/service';
import { authActionClient } from '@/core/server/actions/safe-action';

export const createUserServiceAction = authActionClient
  .schema(serviceFormSchema)
  .metadata({ name: 'create-user-service' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const { payRateId, ...data } = parsedInput;

    const userServiceId = await db.transaction(async (tx) => {
      const [newUserService] = await tx
        .insert(userServiceTable)
        .values({
          userId,
          ...data,
          payRateId: payRateId ? Number.parseInt(payRateId) : null,
        })
        .returning({ userServiceId: userServiceTable.id });

      const { userServiceId } = newUserService ?? {};

      if (!userServiceId) {
        throw new Error('❌ FAILED TO CREATE USER SERVICE');
      }

      return userServiceId;
    });

    logger.info(
      { userId, userServiceId },
      '✅ SUCCESSFULLY CREATED USER SERVICE'
    );

    await syncTypesenseWithSupabase(userId);
  });

const updateUserServiceFormSchema = serviceFormSchema.extend({
  userServiceId: z.number(),
});

export const updateUserServiceAction = authActionClient
  .schema(updateUserServiceFormSchema)
  .metadata({ name: 'update-user-service' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const { userServiceId, fixedPrice, payRateId, ...data } = parsedInput;

    await db.transaction(async (tx) => {
      await tx
        .update(userServiceTable)
        .set({
          ...data,
          fixedPrice: fixedPrice || null,
          payRateId: payRateId ? Number.parseInt(payRateId) : null,
        })
        .where(eq(userServiceTable.id, userServiceId));
    });

    logger.info(
      { userId, userServiceId },
      '✅ SUCCESSFULLY UPDATED USER SERVICE'
    );

    await syncTypesenseWithSupabase(userId);
  });

const deleteServiceSchema = z.object({
  userServiceId: z.number(),
});

export const deleteUserServiceAction = authActionClient
  .schema(deleteServiceSchema)
  .metadata({ name: 'delete-user-service' })
  .action(async ({ ctx: { authUser }, parsedInput: { userServiceId } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .delete(userServiceTable)
        .where(eq(userServiceTable.id, userServiceId));
    });

    logger.info(
      { userId, userServiceId },
      '✅ SUCCESSFULLY DELETED USER SERVICE'
    );

    await syncTypesenseWithSupabase(userId);
  });
