'use server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { usersTable } from '@packages/database/tables';
import { renderOnboardingEmail } from '@packages/email';
import { sendEmail } from '@packages/mailer';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { setUserAppMetadata } from '@packages/supabase/auth-fns/set-user-app-metadata';
import { logger } from '@packages/utils/logger';

import { authActionClient } from '@/core/server/actions/safe-action';

export const completeOnboardingAction = authActionClient
  .metadata({ name: 'complete-onboarding' })
  .action(async ({ ctx: { authUser } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    logger.info({ userId }, 'Completing onboarding for user...');

    try {
      await setUserAppMetadata(userId, { isOnboarded: true });

      await sendOnboardingEmail({
        email: authUser.email as string,
      });

      logger.info({ userId }, 'Successfully sent onboarding email.');

      await db.transaction(async (tx) => {
        await tx
          .update(usersTable)
          .set({ obEmailSent: true })
          .where(eq(usersTable.id, userId));
      });

      await syncTypesenseWithSupabase(userId);

      logger.info({ userId }, 'Onboarding successfully completed for user');
    } catch (error) {
      logger.error({ userId, error }, 'Error completing onboarding for user');
      throw error;
    }
  });

async function sendOnboardingEmail(params: { email: string }) {
  const subject = 'Request for Account Creation Received';
  const from = process.env.EMAIL_SENDER;

  if (!from) {
    throw new Error('Missing EMAIL_SENDER env variable.');
  }

  const onboardingEmail = await renderOnboardingEmail({
    receiverEmail: params.email,
  });

  return sendEmail({
    to: params.email,
    subject,
    contentType: 'html',
    content: onboardingEmail,
    from,
  });
}
