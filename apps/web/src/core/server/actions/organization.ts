'use server';

import type { Maybe } from '@/types';

import { MembershipRole } from '@packages/database/enums';
import { nanoid } from 'nanoid';
import { revalidatePath, revalidateTag } from 'next/cache';
import { RedirectType, redirect } from 'next/navigation';
import { z } from 'zod';

import { createDrizzleSupabaseClient } from '@packages/database';
import { and, eq, not } from '@packages/database/drizzle-orm';
import {
  createOrganization,
  inviteMembersToOrganization,
  transferOwnership,
  updateOrganization,
} from '@packages/database/functions';
import {
  membershipTable,
  organizationSubscriptionTable,
  organizationTable,
} from '@packages/database/tables';
import { renderInviteEmail } from '@packages/email';
import { sendEmail } from '@packages/mailer';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { PostgrestError } from '@packages/supabase';
import { logger } from '@packages/utils/logger';

import { SiteConfig } from '@/configuration';
import { inviteEmployeesFormSchema } from '@/core/schemas/invitation';
import { organizationInfoFormSchema } from '@/core/schemas/organization';
import { authActionClient } from '@/core/server/actions/safe-action';
import { deleteOrganization } from '@/core/server/functions/organization/delete-organization';

import { CacheKey, Caching } from '@/lib/caching';
import { stripe } from '@/lib/stripe';

export const createOrganizationAction = authActionClient
  .schema(organizationInfoFormSchema)
  .metadata({ name: 'create-organization' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const { organizationName, organizationDescription, ...data } = parsedInput;
    const organizationId = await createOrganization(db, {
      userId,
      name: organizationName,
      description: organizationDescription,
      ...data,
    });

    logger.info(
      { userId, organizationId },
      'Successfully created organization'
    );

    revalidateTag(Caching.createTag(CacheKey.OnboardingData, userId));

    return organizationId;
  });

const updateOrganizationFormSchema = organizationInfoFormSchema.extend({
  id: z.string(),
});

export const updateOrganizationAction = authActionClient
  .schema(updateOrganizationFormSchema)
  .metadata({ name: 'update-organization' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const {
      id: organizationId,
      organizationName,
      organizationDescription,
      ...data
    } = parsedInput;

    const db = await createDrizzleSupabaseClient();

    await updateOrganization(db, {
      organizationId,
      name: organizationName,
      description: organizationDescription,
      ...data,
    });

    logger.info(
      { userId, organizationId },
      'Successfully updated organization information'
    );

    await syncTypesenseWithSupabase(userId);
  });

export const inviteMembersToOrganizationAction = authActionClient
  .schema(inviteEmployeesFormSchema)
  .metadata({ name: 'invite-members-to-organization' })
  .action(
    async ({ ctx: { authUser }, parsedInput: { members, shouldRedirect } }) => {
      const inviterId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      const membersWithCode = members.map((member) => ({
        ...member,
        code: nanoid(10),
      }));

      try {
        const { emails, organization, user } =
          await inviteMembersToOrganization(db, {
            inviterId,
            members: membersWithCode,
          });

        logger.info(
          { emails },
          '✅ SUCCESSFULLY INVITED MEMBERS TO ORGANIZATION'
        );

        const inviter = `${user?.firstName ?? ''} ${user?.lastName ?? ''}`;

        const invitedUsers = membersWithCode
          .filter((member) => emails.includes(member.email))
          .map((member) => ({
            invitedUser: getInvitedUserFullName(member),
            organizationLogo: organization?.logo ?? undefined,
            organizationName: organization?.name,
            invitedUserEmail: member.email,
            code: member.code,
            inviter,
          }));

        await Promise.all([
          ...invitedUsers.map((data) => sendInviteEmail(data)),
        ]);

        logger.info(
          { organizationId: organization.id },
          '✅ SUCCESSFULLY INVITED MEMBERS TO ORGANIZATION'
        );
      } catch (error) {
        if (error instanceof PostgrestError || error instanceof Error) {
          logger.error({ error }, '❌ ERROR INVITING USER TO ORGANIZATION');
        } else {
          logger.error(
            { error: String(error) },
            '❌ ERROR INVITING USER TO ORGANIZATION'
          );
        }

        throw error;
      }

      if (shouldRedirect) {
        const redirectPath = SiteConfig.paths.settings.invitations;
        revalidatePath(redirectPath, 'layout');
      }
    }
  );

const transferOrganizationOwnershipBodySchema = z.object({
  membershipId: z.coerce.number(),
  organizationId: z.string().uuid(),
});

export const transferOrganizationOwnershipAction = authActionClient
  .schema(transferOrganizationOwnershipBodySchema)
  .metadata({ name: 'transfer-organization-ownership' })
  .action(
    async ({
      ctx: { authUser },
      parsedInput: { membershipId, organizationId },
    }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      await transferOwnership(db, {
        organizationId,
        targetUserMembershipId: membershipId,
      });

      logger.info(
        {
          userId,
          organizationId,
        },
        '✅ OWNERSHIP TRANSFERRED TO TARGET USER'
      );

      const appHome = SiteConfig.paths.app.root;
      const path = SiteConfig.paths.settings.employees;
      const pathToRevalidate = [appHome, organizationId, path].join('/');

      revalidatePath(pathToRevalidate, 'page');
    }
  );

export const leaveOrganizationAction = authActionClient
  .schema(z.object({ organizationId: z.string().uuid() }))
  .metadata({ name: 'leave-organization' })
  .action(async ({ ctx: { authUser }, parsedInput: { organizationId } }) => {
    const userId = authUser.id;
    const redirectPath = SiteConfig.paths.app.root;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .delete(membershipTable)
        .where(
          and(
            eq(membershipTable.organizationId, organizationId),
            eq(membershipTable.userId, userId),
            not(eq(membershipTable.roleId, MembershipRole.Owner))
          )
        );
    });

    logger.info(
      { userId, organizationId },
      'User successfully left organization'
    );

    return redirect(redirectPath, RedirectType.replace);
  });

export const deleteOrganizationAction = authActionClient
  .schema(z.object({ organizationId: z.string().uuid() }))
  .metadata({ name: 'delete-organization' })
  .action(async ({ ctx: { authUser }, parsedInput: { organizationId } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const subscriptionId = await db.transaction(async (tx) => {
      const [membership] = await tx
        .select({
          id: membershipTable.id,
          role: membershipTable.roleId,
        })
        .from(membershipTable)
        .where(
          and(
            eq(membershipTable.organizationId, organizationId),
            eq(membershipTable.userId, userId)
          )
        )
        .limit(1);

      if (!membership) {
        logger.error('❌ ERROR: USER IS NOT A MEMBER OF THE ORGANIZATION');
        throw new Error('❌ ERROR: USER IS NOT A MEMBER OF THE ORGANIZATION');
      }

      const isOwner = membership.role === MembershipRole.Owner;

      if (!isOwner) {
        logger.error('❌ ERROR: USER IS NOT THE OWNER OF THE ORGANIZATION');
        throw new Error('❌ ERROR: USER IS NOT THE OWNER OF THE ORGANIZATION');
      }

      await deleteOrganization(tx, { organizationId });
    });

    return redirect(SiteConfig.paths.app.root, RedirectType.replace);
  });

// -------------------------------------------------------------------------------
//                              HELPER FUNCTIONS
// -------------------------------------------------------------------------------
interface InviteEmailProps {
  code: string;
  invitedUser: string;
  invitedUserEmail: string;
  organizationName: string;
  organizationLogo: Maybe<string>;
  inviter: string;
}

async function sendInviteEmail(props: InviteEmailProps) {
  const sender = process.env.EMAIL_SENDER;
  if (!sender) {
    throw new Error('Missing EMAIL_SENDER environment variable');
  }

  const inviteLink = new URL(`/invite/${props.code}`, SiteConfig.site.url).href;

  const content = await renderInviteEmail({
    ...props,
    inviteLink,
  });

  return sendEmail({
    to: props.invitedUserEmail,
    from: sender,
    subject: `${props.inviter} has invited you to join ${props.organizationName}!`,
    contentType: 'html',
    content,
  });
}

function getInvitedUserFullName(member: {
  firstName: string;
  lastName: string;
}) {
  const first =
    member.firstName.charAt(0).toUpperCase() + member.firstName.slice(1);
  const last =
    member.lastName.charAt(0).toUpperCase() + member.lastName.slice(1);
  return `${first} ${last}`;
}

async function cancelStripeSubscription(subscriptionId: string) {
  try {
    await stripe.subscriptions.cancel(subscriptionId, {
      invoice_now: true,
    });
  } catch (error) {
    logger.error({ error }, '❌ FAILED TO CANCEL STRIPE SUBSCRIPTION');
    throw error;
  }
}
