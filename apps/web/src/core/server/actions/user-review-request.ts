'use server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { userReviewRequestTable, usersTable } from '@packages/database/tables';
import { renderUserReviewRequestEmail } from '@packages/email';
import { sendEmail } from '@packages/mailer';
import { logger } from '@packages/utils/logger';
import { nanoid } from 'nanoid';
import { z } from 'zod';

import { SiteConfig } from '@/configuration';
import { businessEmailSchema } from '@/core/schemas/miscellaneous';
import { userReviewRequestFormSchema } from '@/core/schemas/user-review-request';
import { authActionClient } from '@/core/server/actions/safe-action';

export const createUserReviewRequestAction = authActionClient
  .schema(userReviewRequestFormSchema)
  .metadata({ name: 'create-user-review-request' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;

    const { reviewerEmail, relationship, message } = parsedInput;

    const token = nanoid(16);
    const expirationDate = getExpirationDate();

    const isClient = authUser.app_metadata.isClient === true;
    if (!isClient) {
      await validateReviewerEmail(reviewerEmail);
    }

    const db = await createDrizzleSupabaseClient();

    const { userReviewRequest, user } = await db.transaction(async (tx) => {
      const [userReviewRequest] = await tx
        .insert(userReviewRequestTable)
        .values({
          requesterId: userId,
          token,
          expirationDate,
          content: message,
          relationship,
          reviewerEmail,
        })
        .returning({ id: userReviewRequestTable.id });

      const [user] = await tx
        .select({
          firstName: usersTable.firstName,
          lastName: usersTable.lastName,
        })
        .from(usersTable)
        .where(eq(usersTable.id, userId))
        .limit(1);

      return { userReviewRequest, user };
    });

    await sendReviewRequestEmail({
      token,
      receiverEmail: reviewerEmail,
      senderFullName: `${user?.firstName} ${user?.lastName}`,
    });

    logger.info(
      { userId, userReviewRequest },
      '✅ SUCCESSFULLY CREATED USER REVIEW REQUEST'
    );
  });

const deleteReviewRequestSchema = z.object({
  id: z.number(),
});

export const deleteUserReviewRequestAction = authActionClient
  .schema(deleteReviewRequestSchema)
  .metadata({ name: 'delete-user-review-request' })
  .action(async ({ ctx: { authUser }, parsedInput: { id } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .delete(userReviewRequestTable)
        .where(eq(userReviewRequestTable.id, id));
    });

    logger.info(
      { userId, userReviewRequestId: id },
      'Successfully deleted user review request'
    );
  });

// -------------------------------------------------------------------------------
//                              UTILITY FUNCTIONS
// -------------------------------------------------------------------------------

function getExpirationDate() {
  return new Date(Date.now() + 1000 * 60 * 60 * 48);
}

async function validateReviewerEmail(reviewerEmail: string) {
  const body = await businessEmailSchema.safeParseAsync(reviewerEmail);
  if (!body.success) {
    throw new Error(`Invalid email address: ${reviewerEmail}`);
  }
}

async function sendReviewRequestEmail(props: {
  senderFullName: string;
  receiverEmail: string;
  token: string;
}) {
  const sender = process.env.EMAIL_SENDER;
  if (!sender) {
    throw new Error(
      'Missing email configuration. Please add the following environment variables: EMAIL_SENDER'
    );
  }

  const { senderFullName, receiverEmail, token } = props;
  const subject = 'You have been requested to provide a review!';
  const reviewPagePath = `${SiteConfig.paths.review}/${token}`;
  const reviewPageLink = new URL(reviewPagePath, SiteConfig.site.url).href;

  const content = await renderUserReviewRequestEmail({
    receiverEmail,
    reviewPageLink,
    senderFullName,
  });

  try {
    await sendEmail({
      to: receiverEmail,
      from: sender,
      subject,
      contentType: 'html',
      content,
    });
  } catch (error) {
    logger.error({ error }, 'Error sending review request email');
    throw error;
  }
}
