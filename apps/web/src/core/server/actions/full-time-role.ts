'use server';

import { createDrizzleSupabaseClient } from '@packages/database';
import {
  createFullTimeRole,
  deleteFullTimeRole,
  updateFullTimeRole,
} from '@packages/database/functions';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { fullTimeRoleFormSchema } from '@/core/schemas/full-time-role';
import { authActionClient } from '@/core/server/actions/safe-action';

export const createFullTimeRoleAction = authActionClient
  .schema(fullTimeRoleFormSchema)
  .metadata({ name: 'create-full-time-role' })
  .action(
    async ({
      ctx: { authUser },
      parsedInput: { position, payRateId, desiredStartDate, locationType },
    }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      const fullTimeRoleId = await createFullTimeRole(db, {
        userId,
        positionId: position.id,
        positionName: position.name,
        payRateId: Number.parseInt(payRateId),
        desiredStartDate: desiredStartDate?.toISOString(),
        locationType,
      });

      logger.info(
        { userId, fullTimeRoleId },
        '✅ SUCCESSFULLY CREATED FULL TIME AVAILABILITY'
      );

      await syncTypesenseWithSupabase(userId);
    }
  );

const updateFullTimeRoleFormSchema = fullTimeRoleFormSchema.extend({
  fullTimeAvailabilityId: z.number(),
});

export const updateFullTimeRoleAction = authActionClient
  .schema(updateFullTimeRoleFormSchema)
  .metadata({ name: 'update-full-time-role' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const {
      position,
      payRateId,
      desiredStartDate,
      locationType,
      fullTimeAvailabilityId,
    } = parsedInput;

    const fullTimeRoleId = await updateFullTimeRole(db, {
      userId,
      fullTimeAvailabilityId,
      positionId: position.id,
      positionName: position.name,
      payRateId: Number.parseInt(payRateId),
      desiredStartDate: desiredStartDate?.toISOString(),
      locationType,
    });

    logger.info(
      { userId, fullTimeRoleId },
      '✅ SUCCESSFULLY UPDATED FULL TIME AVAILABILITY'
    );

    await syncTypesenseWithSupabase(userId);
  });

const deleteFullTimeRoleFormSchema = z.object({
  fullTimeAvailabilityId: z.number(),
});

export const deleteFullTimeRoleAction = authActionClient
  .schema(deleteFullTimeRoleFormSchema)
  .metadata({ name: 'delete-full-time-role' })
  .action(
    async ({ ctx: { authUser }, parsedInput: { fullTimeAvailabilityId } }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      await deleteFullTimeRole(db, { fullTimeAvailabilityId });

      logger.info(
        { userId, fullTimeAvailabilityId },
        '✅ SUCCESSFULLY DELETED USER FULL TIME AVAILABILITY'
      );

      await syncTypesenseWithSupabase(userId);
    }
  );
