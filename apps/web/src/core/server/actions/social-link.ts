'use server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { upsertOrganizationSocialLink } from '@packages/database/functions';
import { socialLinkTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { socialLinkFormSchema } from '@/core/schemas/social-link';
import { authActionClient } from '@/core/server/actions/safe-action';

export const upsertSocialLinkAction = authActionClient
  .schema(socialLinkFormSchema)
  .metadata({ name: 'upsert-social-link' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx.insert(socialLinkTable).values(
        parsedInput.urls.map(({ url }) => ({
          userId,
          url,
        }))
      );
    });

    logger.info({ userId }, 'Successfully upserted social link');
  });

export const upsertOrganizationSocialLinkAction = authActionClient
  .schema(socialLinkFormSchema)
  .metadata({ name: 'upsert-organization-social-link' })
  .action(async ({ ctx: { authUser }, parsedInput: data }) => {
    const userId = authUser.id;
    const urls = data.urls?.map(({ url }) => url);

    const db = await createDrizzleSupabaseClient();

    await upsertOrganizationSocialLink(db, {
      userId,
      urls,
    });

    logger.info({ userId }, 'Successfully upserted social link');
  });

const deleteSocialLinkSchema = z.object({
  id: z.number(),
});

export const deleteSocialLinkAction = authActionClient
  .schema(deleteSocialLinkSchema)
  .metadata({ name: 'delete-social-link' })
  .action(async ({ parsedInput: { id } }) => {
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx.delete(socialLinkTable).where(eq(socialLinkTable.id, id));
    });

    logger.info({ id }, 'Successfully deleted social link');
  });
