'use server';

import { contactFormSchema } from '@/core/schemas/contact-form';
import { rateLimitedActionClient } from '@/core/server/actions/safe-action';
import { sendEmail } from '@packages/mailer';
import type { EmailType } from '@packages/mailer/schema';
import { logger } from '@packages/utils/logger';

export const sendContactFormEmailAction = rateLimitedActionClient
  .schema(contactFormSchema)
  .metadata({ name: 'send-contact-form-email' })
  .action(async ({ parsedInput }) => {
    try {
      const email: EmailType = {
        from: parsedInput.email,
        to: '<EMAIL>',
        subject: `Contact Request from ${parsedInput.name}`,
        contentType: 'text',
        content: parsedInput.message,
      };

      await sendEmail(email);
      logger.info('Successfully sent contact form email');
      return { success: true, message: 'Successfully sent contact form email' };
    } catch (error) {
      logger.error('Failed to send contact form email', { error });
      return { success: false, message: 'Failed to send contact form email' };
    }
  });
