'use server';

import type { DrizzleSupabaseClient } from '@packages/database';
import type { Supabase } from '@packages/supabase/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { and, eq } from '@packages/database/drizzle-orm';
import {
  acceptConnectionRequest,
  createConnectionRequest,
} from '@packages/database/functions';
import { connectionRequestTable } from '@packages/database/tables';
import { getSupabaseServerClient } from '@packages/supabase/server';
import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { authActionClient } from '@/core/server/actions/safe-action';
import { getNotificationsApi } from '@/lib/notifications/server/api';
import { NotificationType } from '@/lib/notifications/types';

const createConnectionRequestSchema = z.object({
  receiverId: z.string().uuid(),
});

export const createConnectionRequestAction = authActionClient
  .schema(createConnectionRequestSchema)
  .metadata({ name: 'create-connection-request' })
  .action(async ({ ctx: { authUser }, parsedInput: { receiverId } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();
    const supabaseAdmin = await getSupabaseServerClient({ admin: true });

    const isClient = authUser.app_metadata.isClient === true;

    await validateIfBothUsersAreConsultants(
      supabaseAdmin,
      receiverId,
      isClient
    );

    const connectionRequestId = await createConnectionRequest(db, {
      requesterId: userId,
      receiverId,
    });

    logger.info(
      { userId, connectionRequestId },
      '✅ SUCCESSFULLY CREATED CONNECTION REQUEST'
    );
  });

const acceptConnectionRequestSchema = z.object({
  connectionRequestId: z.number(),
  requesterId: z.string(),
});

export const acceptConnectionRequestAction = authActionClient
  .schema(acceptConnectionRequestSchema)
  .metadata({ name: 'accept-connection-request' })
  .action(async ({ ctx: { authUser }, parsedInput: data }) => {
    const userId = authUser.id;
    const dbAdmin = await createDrizzleSupabaseClient({ admin: true });

    const { connectionId, chatRoomId, receiver } =
      await acceptConnectionRequest(dbAdmin, {
        receiverId: userId,
        ...data,
      });

    await sendConnectionAcceptedNotification({
      dbAdmin,
      requesterId: data.requesterId,
      receiverId: userId,
      chatRoomId,
      receiver,
    });

    logger.info(
      { userId, connectionId },
      '✅ SUCCESSFULLY ACCEPTED CONNECTION REQUEST'
    );
  });

const deleteConnectionRequestSchema = z.union([
  z.object({
    connectionRequestId: z.number(),
  }),
  z.object({
    receiverId: z.string().uuid(),
  }),
]);

export const deleteConnectionRequestAction = authActionClient
  .schema(deleteConnectionRequestSchema)
  .metadata({ name: 'delete-connection-request' })
  .action(async ({ ctx: { authUser }, parsedInput: data }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    if ('connectionRequestId' in data) {
      await db.transaction(async (tx) => {
        await tx
          .delete(connectionRequestTable)
          .where(eq(connectionRequestTable.id, data.connectionRequestId));
      });
    }

    if ('receiverId' in data) {
      await db.transaction(async (tx) => {
        await tx
          .delete(connectionRequestTable)
          .where(
            and(
              eq(connectionRequestTable.requesterId, userId),
              eq(connectionRequestTable.receiverId, data.receiverId)
            )
          );
      });
    }

    logger.info({ userId }, '✅ SUCCESSFULLY DELETED CONNECTION REQUEST');
  });

export const markConnectionRequestsAsReadAction = authActionClient
  .metadata({ name: 'mark-connection-requests-as-read' })
  .action(async ({ ctx: { authUser } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .update(connectionRequestTable)
        .set({ isRead: true })
        .where(
          and(
            eq(connectionRequestTable.receiverId, userId),
            eq(connectionRequestTable.isRead, false)
          )
        );
    });

    logger.info(
      { userId },
      '✅ SUCCESSFULLY MARKED CONNECTION REQUESTS AS READ'
    );
  });

// -------------------------------------------------------------------------------
//                              UTILITY FUNCTIONS
// -------------------------------------------------------------------------------

async function validateIfBothUsersAreConsultants(
  supabaseAdmin: Supabase,
  receiverId: string,
  isRequesterClient: boolean
) {
  const { data: receiver, error } =
    await supabaseAdmin.auth.admin.getUserById(receiverId);

  if (error || !receiver) {
    logger.error({ error }, '❌ ERROR GETTING RECEIVER RECORD');
    throw error;
  }

  const isReceiverClient = receiver.user.app_metadata.isClient === true;

  if (!isRequesterClient && !isReceiverClient) {
    throw new Error('BOTH USERS ARE CONSULTANTS');
  }
}

async function sendConnectionAcceptedNotification(params: {
  dbAdmin: DrizzleSupabaseClient;
  requesterId: string;
  receiverId: string;
  chatRoomId: string;
  receiver?: {
    avatar: string | null;
    firstName: string | null;
    lastName: string | null;
    username: string | null;
  };
}) {
  const { dbAdmin, requesterId, receiverId, chatRoomId, receiver } = params;
  const receiverFullName = `${receiver?.firstName} ${receiver?.lastName}`;
  const notificationApi = getNotificationsApi(dbAdmin);

  await notificationApi.sendNotification({
    userId: requesterId,
    type: NotificationType.ConnectionRequestAccepted,
    content: `Your connection request to ${receiverFullName} has been accepted`,
    metadata: {
      senderId: receiverId,
      senderName: receiverFullName,
      senderUsername: receiver?.username,
      senderAvatar: receiver?.avatar,
      chatRoomId,
    },
  });
}
