'use server';

import { permanentRedirect } from 'next/navigation';
import { z } from 'zod';

import { createDrizzleSupabaseClient } from '@packages/database';
import { createUserReview } from '@packages/database/functions';
import { renderUserReviewConfirmationEmail } from '@packages/email';
import { sendEmail } from '@packages/mailer';
import { getSupabaseServerClient } from '@packages/supabase/server';

import { logger } from '@packages/utils/logger';

import { SiteConfig } from '@/configuration';
import { userReviewFormSchema } from '@/core/schemas/user-review';
import {
  authActionClient,
  rateLimitedActionClient,
} from '@/core/server/actions/safe-action';
import { getNotificationsApi } from '@/lib/notifications/server/api';
import { NotificationType } from '@/lib/notifications/types';
import { eq } from '@packages/database/drizzle-orm';
import { userReviewTable } from '@packages/database/tables';

const createUserReviewAsAdminSchema = userReviewFormSchema.extend({
  userReviewRequestId: z.number(),
  requesterId: z.string().uuid(),
  relationship: z.string(),
});

export const createUserReviewAsAdminAction = rateLimitedActionClient
  .metadata({ name: 'create-user-review-as-admin' })
  .schema(createUserReviewAsAdminSchema)
  .action(async ({ parsedInput: data }) => {
    const supabase = await getSupabaseServerClient();
    const dbAdmin = await createDrizzleSupabaseClient({ admin: true });

    const { data: authUser } = await supabase.auth.getUser();
    const reviewerId = authUser.user?.id as string;

    const { userReviewId, user } = await createUserReview(dbAdmin, {
      userId: data.requesterId,
      reviewerId,
      ...data,
    });

    logger.info({ userReviewId }, '✅ SUCCESSFULLY CREATED USER REVIEW');

    const reviewerFullName = `${data.reviewerFirstName} ${data.reviewerLastName}`;
    await sendUserReviewConfirmationEmail(data.reviewerEmail, reviewerFullName);
    logger.info('✅ SUCCESSFULLY SENT USER REVIEW CONFIRMATION EMAIL');

    const notificationApi = getNotificationsApi(dbAdmin);

    const metadata = {
      senderId: reviewerId,
      senderName: reviewerFullName,
      senderEmail: data.reviewerEmail,
      overallRating: getOverallRating(data),
      senderUsername: user?.username,
      senderAvatar: user?.avatar,
    };

    await notificationApi.sendNotification({
      userId: data.requesterId,
      type: NotificationType.UserReviewSubmission,
      content: `New review submitted from ${reviewerFullName}`,
      metadata,
    });

    permanentRedirect(SiteConfig.paths.root);
  });

const acceptUserReviewSchema = z.object({
  id: z.number(),
});

export const acceptUserReviewAction = authActionClient
  .schema(acceptUserReviewSchema)
  .metadata({ name: 'accept-user-review' })
  .action(async ({ ctx: { authUser }, parsedInput: { id } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .update(userReviewTable)
        .set({ status: 'ACCEPTED' })
        .where(eq(userReviewTable.id, id));
    });

    logger.info({ userId }, 'Successfully accepted user review');
  });

const deleteReviewSchema = z.object({
  id: z.number(),
});

export const deleteUserReviewAction = authActionClient
  .schema(deleteReviewSchema)
  .metadata({ name: 'delete-user-review' })
  .action(async ({ ctx: { authUser }, parsedInput: { id } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx.delete(userReviewTable).where(eq(userReviewTable.id, id));
    });

    logger.info({ userId }, 'Successfully deleted user review');
  });

// -------------------------------------------------------------------------------
//                              UTILITY FUNCTIONS
// -------------------------------------------------------------------------------

function getOverallRating(data: z.infer<typeof createUserReviewAsAdminSchema>) {
  return (
    (data.professionalismRating +
      data.communicationRating +
      data.qualityOfWorkRating +
      data.timelinessRating +
      data.reliabilityRating) /
    5
  );
}

export async function sendUserReviewConfirmationEmail(
  receiverEmail: string,
  receiverFullName: string
) {
  const sender = process.env.EMAIL_SENDER;
  if (!sender) {
    throw new Error(
      'Missing email configuration. Please add the following environment variables: EMAIL_SENDER'
    );
  }

  try {
    const subject = 'You have been requested to provide a review!';
    const content = await renderUserReviewConfirmationEmail({
      receiverEmail,
      receiverFullName,
    });

    return sendEmail({
      to: receiverEmail,
      from: sender,
      subject,
      contentType: 'html',
      content,
    });
  } catch (error) {
    logger.error({ error }, 'Error sending user review confirmation email');
    throw error;
  }
}
