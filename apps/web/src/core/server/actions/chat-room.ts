'use server';

import { z } from 'zod';

import { createDrizzleSupabaseClient } from '@packages/database';
import { and, eq } from '@packages/database/drizzle-orm';
import { chatRoomMemberTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { authActionClient } from '@/core/server/actions/safe-action';

const archiveChatSchema = z.object({
  roomId: z.string().min(1),
  isArchived: z.boolean(),
});

export const toggleArchiveChatAction = authActionClient
  .schema(archiveChatSchema)
  .metadata({ name: 'toggle-archive-chat' })
  .action(
    async ({ ctx: { authUser }, parsedInput: { roomId, isArchived } }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      await db.transaction(async (tx) => {
        await tx
          .update(chatRoomMemberTable)
          .set({ isArchived })
          .where(eq(chatRoomMemberTable.roomId, roomId));
      });

      logger.info({ userId, roomId }, '✅ SUCCESSFULLY ARCHIVED CHAT');
    }
  );

export const markChatAsReadAction = authActionClient
  .schema(z.object({ roomId: z.string().min(1) }))
  .metadata({ name: 'mark-chat-as-read' })
  .action(async ({ ctx: { authUser }, parsedInput: { roomId } }) => {
    const userId = authUser.id;

    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .update(chatRoomMemberTable)
        .set({ hasRead: true })
        .where(
          and(
            eq(chatRoomMemberTable.userId, userId),
            eq(chatRoomMemberTable.roomId, roomId)
          )
        );
    });

    logger.info({ userId, roomId }, '✅ SUCCESSFULLY MARKED CHAT AS READ');
  });
