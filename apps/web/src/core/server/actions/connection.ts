'use server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { deleteConnection } from '@packages/database/functions';
import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { authActionClient } from '@/core/server/actions/safe-action';

const deleteConnectionSchema = z.object({
  connectionId: z.number(),
});

export const deleteConnectionAction = authActionClient
  .schema(deleteConnectionSchema)
  .metadata({ name: 'delete-connection' })
  .action(async ({ ctx: { authUser }, parsedInput: { connectionId } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient({ admin: true });

    const { chatRoomId } = await deleteConnection(db, { connectionId });

    logger.info(
      { userId, connectionId, chatRoomId },
      '✅ SUCCESSFULLY DELETED CONNECTION'
    );
  });
