'use server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  createUserCertification,
  updateUserCertification,
} from '@packages/database/functions';
import { userCertificationTable } from '@packages/database/tables';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { certicationFormSchema } from '@/core/schemas/certification';
import { authActionClient } from '@/core/server/actions/safe-action';

export const createUserCertificationAction = authActionClient
  .schema(certicationFormSchema)
  .metadata({ name: 'create-user-certification' })
  .action(
    async ({
      ctx: { authUser },
      parsedInput: { issuedDate, expirationDate, ...data },
    }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      const certificationId = await createUserCertification(db, {
        userId,
        ...data,
        issuedDate: issuedDate?.toISOString(),
        expirationDate: expirationDate?.toISOString(),
      });

      logger.info(
        { userId, certificationId },
        '✅ SUCCESSFULLY CREATED USER CERTIFICATION'
      );

      await syncTypesenseWithSupabase(userId);
    }
  );

const updateCertificationFormSchema = certicationFormSchema.extend({
  certificationId: z.number(),
});

export const updateUserCertificationAction = authActionClient
  .schema(updateCertificationFormSchema)
  .metadata({ name: 'update-user-certification' })
  .action(
    async ({
      ctx: { authUser },
      parsedInput: { certificationId, issuedDate, expirationDate, ...data },
    }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      await updateUserCertification(db, {
        userId,
        ...data,
        certificationId,
        issuedDate: issuedDate?.toISOString(),
        expirationDate: expirationDate?.toISOString(),
      });

      logger.info(
        { userId, certificationId },
        '✅ SUCCESSFULLY UPDATED USER CERTIFICATION'
      );

      await syncTypesenseWithSupabase(userId);
    }
  );

const deleteCertificationFormSchema = z.object({
  id: z.number(),
});

export const deleteUserCertificationAction = authActionClient
  .schema(deleteCertificationFormSchema)
  .metadata({ name: 'delete-user-certification' })
  .action(async ({ ctx: { authUser }, parsedInput: { id } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    await db.transaction(async (tx) => {
      await tx
        .delete(userCertificationTable)
        .where(eq(userCertificationTable.id, id));
    });

    logger.info(
      { userId, userCertificationId: id },
      '✅ SUCCESSFULLY DELETED USER CERTIFICATION'
    );

    await syncTypesenseWithSupabase(userId);
  });
