'use server';

import { z } from 'zod';

import { createDrizzleSupabaseClient } from '@packages/database';
import { and, eq } from '@packages/database/drizzle-orm';
import {
  createPastPerformance,
  updatePastPerformance,
} from '@packages/database/functions';
import { governmentContractTable } from '@packages/database/tables';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { logger } from '@packages/utils/logger';

import {
  basePastPerformanceFormSchema,
  pastPerformanceFormSchema,
} from '@/core/schemas/past-performance';
import { authActionClient } from '@/core/server/actions/safe-action';

export const createPastPerformanceAction = authActionClient
  .schema(pastPerformanceFormSchema)
  .metadata({ name: 'create-past-performance' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const {
      position,
      office,
      agency,
      subOffice,
      department,
      awardDate,
      ...data
    } = parsedInput;

    const db = await createDrizzleSupabaseClient();

    const response = await createPastPerformance(db, {
      userId,
      ...data,
      officeId: office?.id,
      agencyId: agency?.id,
      subOfficeId: subOffice?.id,
      positionId: position?.id,
      positionName: position.name,
      departmentId: department?.id,
      awardDate: awardDate.toISOString(),
    });

    logger.info(
      { userId, ...response },
      '✅ SUCCESSFULLY CREATED USER PAST PERFORMANCE'
    );

    await syncTypesenseWithSupabase(userId);
  });

const updatePastPerformanceFormSchema = basePastPerformanceFormSchema.extend({
  pastPerformanceId: z.number(),
});

export const updatePastPerformanceAction = authActionClient
  .schema(updatePastPerformanceFormSchema)
  .metadata({ name: 'update-past-performance' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const {
      position,
      office,
      agency,
      subOffice,
      department,
      awardDate,
      ...data
    } = parsedInput;

    const db = await createDrizzleSupabaseClient();

    const response = await updatePastPerformance(db, {
      ...data,
      officeId: office?.id,
      agencyId: agency?.id,
      subOfficeId: subOffice?.id,
      positionId: position?.id,
      positionName: position.name,
      departmentId: department?.id,
      awardDate: awardDate.toISOString(),
    });

    logger.info(
      { userId, ...response },
      '✅ SUCCESSFULLY UPDATED USER PAST PERFORMANCE'
    );

    await syncTypesenseWithSupabase(userId);
  });

const deletePastPerformanceSchema = z.object({
  governmentContractId: z.number(),
});

export const deletePastPerformanceAction = authActionClient
  .schema(deletePastPerformanceSchema)
  .metadata({ name: 'delete-past-performance' })
  .action(
    async ({ ctx: { authUser }, parsedInput: { governmentContractId } }) => {
      const userId = authUser.id;
      const db = await createDrizzleSupabaseClient();

      await db.transaction(async (tx) => {
        await tx
          .delete(governmentContractTable)
          .where(and(eq(governmentContractTable.id, governmentContractId)));
      });

      logger.info(
        { governmentContractId },
        '✅ SUCCESSFULLY DELETED USER PAST PERFORMANCE'
      );

      await syncTypesenseWithSupabase(userId);

      return governmentContractId;
    }
  );
