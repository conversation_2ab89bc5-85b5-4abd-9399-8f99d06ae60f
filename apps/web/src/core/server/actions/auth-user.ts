'use server';

import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { logger } from '@packages/utils/logger';
import { z } from 'zod';

import { SiteConfig } from '@/configuration';
import {
  updateEmailFormSchema,
  updatePasswordFormSchema,
} from '@/core/schemas/security';
import { authActionClient } from '@/core/server/actions/safe-action';

export const updateEmailAction = authActionClient
  .schema(updateEmailFormSchema)
  .metadata({ name: 'update-email' })
  .action(
    async ({ ctx: { supabase, authUser }, parsedInput: { newEmail } }) => {
      const userId = authUser.id;

      if (newEmail === authUser.email) {
        logger.error(
          { userId, newEmail },
          'same_email: Email is the same as the current one. No changes made.'
        );
        throw new Error(
          'same_email: New email is the same as the current one.'
        );
      }

      const { error } = await supabase.auth.updateUser(
        { email: newEmail },
        { emailRedirectTo: `${SiteConfig.paths.auth.callback}` }
      );

      if (error) {
        logger.error({ error, userId, newEmail }, 'Error updating user email');
        throw error;
      }

      logger.info({ userId, newEmail }, 'Successfully updated user email');

      await syncTypesenseWithSupabase(userId);
    }
  );

export const updatePasswordAction = authActionClient
  .schema(updatePasswordFormSchema)
  .metadata({ name: 'update-password' })
  .action(
    async ({ ctx: { supabase, authUser }, parsedInput: { newPassword } }) => {
      const userId = authUser.id;

      const { error } = await supabase.auth.updateUser(
        { password: newPassword },
        { emailRedirectTo: SiteConfig.paths.auth.callback }
      );

      if (error) {
        logger.error({ error, userId }, 'Error updating user password');
        if (error.status === 422) {
          throw new Error(
            'same_password: New password must be different from the old password.'
          );
        }
        throw error;
      }

      logger.info({ userId }, 'Successfully updated user password');

      await syncTypesenseWithSupabase(userId);
    }
  );

const updateAuthUserSchema = z.object({
  userAttributes: z.object({
    email: z.string().email().optional(),
    password: z.string().optional(),
  }),
  options: z
    .object({
      emailRedirectTo: z.string().optional(),
    })
    .optional(),
});

export const updateAuthUserAction = authActionClient
  .schema(updateAuthUserSchema)
  .metadata({ name: 'update-auth-user' })
  .action(
    async ({
      ctx: { supabase, authUser },
      parsedInput: { userAttributes, options },
    }) => {
      const userId = authUser.id;

      const { error } = await supabase.auth.updateUser(userAttributes, options);

      if (error) {
        logger.error({ error, userId }, 'Error updating auth user');
        if (error.status === 422) {
          throw new Error(
            'same_password: New password must be different from the old password.'
          );
        }
        throw error;
      }

      await syncTypesenseWithSupabase(userId);

      logger.info({ userId }, 'Successfully updated auth user');
    }
  );
