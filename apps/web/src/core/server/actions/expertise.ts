'use server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { upsertExpertise } from '@packages/database/functions';
import { userSkillTable } from '@packages/database/tables';
import { syncTypesenseWithSupabase } from '@packages/search/sync';
import { logger } from '@packages/utils/logger';

import { expertiseFormSchema } from '@/core/schemas/expertise';
import { authActionClient } from '@/core/server/actions/safe-action';

export const upsertExpertiseAction = authActionClient
  .schema(expertiseFormSchema)
  .metadata({ name: 'upsert-expertise' })
  .action(async ({ ctx: { authUser }, parsedInput }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const expertise = parsedInput.expertise.map(
      ({ skill, expertiseLevel }) => ({
        skillId: skill.id,
        expertiseLevel,
      })
    );

    await upsertExpertise(db, {
      userId,
      expertise,
    });

    logger.info({ userId }, '✅ SUCCESSFULLY UPSERTED USER EXPERTISE');

    await syncTypesenseWithSupabase(userId);
  });

export const deleteAllExpertiseAction = authActionClient
  .metadata({ name: 'delete-all-expertise' })
  .action(async ({ ctx: { authUser } }) => {
    const userId = authUser.id;
    const db = await createDrizzleSupabaseClient();

    const expertiseIds = await db.transaction(async (tx) => {
      const expertise = await tx
        .delete(userSkillTable)
        .where(eq(userSkillTable.userId, userId))
        .returning({ id: userSkillTable.id });
      return expertise.map((expertise) => expertise.id);
    });

    logger.info(
      { userId, expertiseIds },
      '✅ SUCCESSFULLY DELETED USER EXPERTISE'
    );

    await syncTypesenseWithSupabase(userId);
  });
