import type { SubscriptionResponse } from '@packages/database/types';

import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export function useGetCurrentSubscription() {
  const fetchApi = useApiRequest<SubscriptionResponse>();
  return useQuery({
    queryKey: ['current-subscription'],
    queryFn: () =>
      fetchApi({
        path: '/api/subscription',
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
