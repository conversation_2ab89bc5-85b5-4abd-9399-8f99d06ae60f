import type { Supabase } from '@packages/supabase/types';

import { useMutation } from '@tanstack/react-query';

import { useUserId } from '@/core/hooks/user';
import { useSupabase } from '@packages/supabase/hooks';

export const useUploadFeedbackFiles = () => {
  const supabase = useSupabase();
  const userId = useUserId();

  const mutationFn = async (files: File[]) => {
    return uploadFeedbackFiles(supabase, userId!, files);
  };

  return useMutation({ mutationFn });
};

export const uploadFeedbackFiles = async (
  supabase: Supabase,
  userId: string,
  files: File[]
) => {
  const bucket = supabase.storage.from('feedback');

  const uploadPromises = files.map(async (file) => {
    const bytes = await file.arrayBuffer();
    const fileName = `${userId}_${Math.floor(Math.random() * 100000)}`;

    const { error } = await bucket.upload(fileName, bytes, {
      upsert: true,
      contentType: file.type,
    });

    if (error) {
      console.error({ error }, 'Error uploading feedback file');
      throw Error(error.message);
    }

    return bucket.getPublicUrl(fileName).data.publicUrl;
  });

  return await Promise.all(uploadPromises);
};
