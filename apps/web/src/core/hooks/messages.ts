import type { MessageResponse } from '@packages/database/types';

import { useApiRequest } from '@/core/hooks/use-api';
import { useInfiniteQuery } from '@tanstack/react-query';

export function useMessagesByChatRoomIdQuery({
  roomId,
  perPage,
}: {
  perPage: number;
  roomId: string;
}) {
  const fetchApi = useApiRequest<MessageResponse[]>();
  return useInfiniteQuery({
    queryKey: ['message', roomId, perPage],
    queryFn: async ({ pageParam = 1 }) =>
      fetchApi({
        path: `/api/messages?roomId=${roomId}&page=${pageParam}&perPage=${perPage}`,
        method: 'GET',
      }).then((messages) => ({
        messages,
        nextPage: messages.length === perPage ? pageParam + 1 : undefined,
      })),
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 1,
    retry: 1,
    retryDelay: 3000,
  });
}
