import type { UserPastPerformanceResponse } from '@packages/database/types';

import { useApiRequest } from '@/core/hooks/use-api';
import { useQuery } from '@tanstack/react-query';

export function useGetPastPerformancesByUserIdQuery({
  userId,
  initialData,
  enabled,
}: {
  userId: string;
  initialData?: UserPastPerformanceResponse[];
  enabled?: boolean;
}) {
  const fetchApi = useApiRequest<UserPastPerformanceResponse[]>();

  return useQuery({
    queryKey: ['past-performance', userId],
    queryFn: () =>
      fetchApi({ path: `/api/past-performance/${userId}`, method: 'GET' }),
    initialData,
    enabled,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
