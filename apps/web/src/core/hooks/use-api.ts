import type { HttpMethod } from '@/types';
import { useCallback, useContext } from 'react';

import CsrfTokenContext from '@/core/contexts/csrf-context';

export function useApiRequest<Resp = unknown, Body = void>() {
  const csrfToken = useContext(CsrfTokenContext);

  const fetchRequest = async (params: {
    path: string;
    body?: Body;
    method?: HttpMethod;
    headers?: Record<string, string>;
  }) => {
    const options: RequestInit = {
      method: params.method ?? 'POST',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/json',
        ...(params.headers ?? {}),
        ...(csrfToken ? { 'X-CSRF-Token': csrfToken } : {}),
      },
    };

    const supportsBody = ['POST', 'PUT'].includes(params.method ?? 'POST');
    if (params.body && supportsBody) {
      options.body = JSON.stringify(params.body);
    }

    try {
      const response = await fetch(params.path, options);

      if (response.ok) return (await response.json()) as Resp;

      return Promise.reject(await response.json());
    } catch (error) {
      return Promise.reject(error);
    }
  };
  return useCallback(fetchRequest, [csrfToken]);
}
