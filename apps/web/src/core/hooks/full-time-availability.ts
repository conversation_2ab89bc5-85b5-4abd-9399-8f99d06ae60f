import type { FullTimeAvailabilityResponse } from '@packages/database/types';

import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export function useGetFullTimeAvailabilityByUserIdQuery({
  userId,
  initialData,
  enabled,
}: {
  userId: string;
  initialData?: FullTimeAvailabilityResponse[];
  enabled?: boolean;
}) {
  const fetchApi = useApiRequest<FullTimeAvailabilityResponse[]>();
  return useQuery({
    queryKey: ['full-time-availability', userId],
    queryFn: () =>
      fetchApi({
        path: `/api/full-time-availability/${userId}`,
        method: 'GET',
      }),
    initialData,
    enabled,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
