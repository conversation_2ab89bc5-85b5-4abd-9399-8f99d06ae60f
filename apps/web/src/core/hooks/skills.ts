import type { SkillResponse } from '@packages/database/types';
import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export function useGetSkillsByNameQuery(name?: string) {
  const fetchApi = useApiRequest<SkillResponse[]>();
  return useQuery({
    queryKey: ['skills', name],
    queryFn: () =>
      fetchApi({
        path: `/api/skills/${name ?? ''}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}
