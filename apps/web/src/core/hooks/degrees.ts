import type { DegreeResponse } from '@packages/database/types';
import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export function useGetDegreesQuery() {
  const fetchApi = useApiRequest<DegreeResponse[]>();
  return useQuery({
    queryKey: ['degrees'],
    queryFn: () =>
      fetchApi({
        path: '/api/degrees',
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}
