import type { ChatRoomMemberResponse } from '@packages/database/types';
import type { ChatRoomMemberDao } from '@packages/supabase/dao';

import { useSupabase } from '@packages/supabase/hooks';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { usePathname } from 'next/navigation';
import { useEffect } from 'react';

import { SiteConfig } from '@/configuration';
import { useApiRequest } from '@/core/hooks/use-api';

const CHAT_ROOM_MEMBER_TABLE = 'chat_room_member';

export function useMembersByChatRoomIdQuery(roomId: string) {
  const fetchApi = useApiRequest<ChatRoomMemberResponse[]>();
  return useQuery({
    queryKey: ['chat-room-member', roomId],
    queryFn: () =>
      fetchApi({
        path: `/api/chat/room-members/${roomId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export function useGetIsChatArchivedByRoomIdQuery(roomId: string) {
  const fetchApi = useApiRequest<boolean>();
  return useQuery({
    queryKey: ['is-chat-archived', roomId],
    queryFn: () =>
      fetchApi({
        path: `/api/chat/is-archived/${roomId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}

export function useUnreadChatRoomsCount({
  userId,
  isArchived = false,
}: {
  userId: string;
  isArchived?: boolean;
}) {
  const fetchApi = useApiRequest<number>();
  return useQuery({
    queryKey: ['unread-chat-count', userId],
    queryFn: () =>
      fetchApi({
        path: `/api/chat/unread-rooms-count/${userId}?isArchived=${isArchived}`,
        method: 'GET',
      }),
  });
}

export function useStreamUnreadChats({ userId }: { userId: string }) {
  const supabase = useSupabase();
  const queryClient = useQueryClient();
  const pathname = usePathname();

  const isOnMessagesPage = pathname.startsWith(SiteConfig.paths.app.messages);

  useEffect(() => {
    if (!userId) {
      console.error('User not found.');
      return;
    }
    const channel = supabase.channel('chat_room_member');
    channel
      .on<ChatRoomMemberDao>(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: CHAT_ROOM_MEMBER_TABLE,
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          const event = payload.eventType;
          if (event === 'DELETE') return;
          // refetch in case of insert if the message has not been read
          if (event === 'INSERT' && !payload.new.has_read) {
            queryClient.invalidateQueries({
              queryKey: ['unread-chat-count', userId],
            });
          }
          // refetch in case of update if it is not archived, the message was previously read and now it is not
          if (
            event === 'UPDATE' &&
            !payload.new.is_archived &&
            payload.new.has_read !== payload.old.has_read
          ) {
            queryClient.invalidateQueries({
              queryKey: ['unread-chat-count', userId],
            });
            if (isOnMessagesPage) {
              queryClient.invalidateQueries({
                queryKey: ['chat-rooms', userId],
              });
            }
          }
        }
      )
      .subscribe();

    return () => {
      void channel.unsubscribe();
    };
  }, [supabase, userId]);
}
