import type { UserReviewRequestResponse } from '@packages/database/types';

import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export function useGetUserReviewRequestsByRequesterIdQuery(
  requesterId: string
) {
  const fetchApi = useApiRequest<UserReviewRequestResponse[]>();
  return useQuery({
    queryKey: ['user-review-requests', requesterId],
    queryFn: () =>
      fetchApi({
        path: `/api/user-review-requests/${requesterId}`,
        method: 'GET',
      }),
  });
}
