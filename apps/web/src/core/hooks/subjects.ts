import type { SubjectResponse } from '@packages/database/types';
import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export function useGetSubjectsByNameQuery(name: string) {
  const fetchApi = useApiRequest<SubjectResponse[]>();
  return useQuery({
    queryKey: ['subjects', name],
    queryFn: () =>
      fetchApi({
        path: `/api/subjects/${name}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}
