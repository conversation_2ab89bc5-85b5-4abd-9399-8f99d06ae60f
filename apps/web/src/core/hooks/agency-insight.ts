import type { AgencyInsightResponse } from '@packages/database/types';
import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

interface UseGetAgencyInsightByUserIdQueryProps {
  userId: string;
  initialData?: AgencyInsightResponse[];
  enabled?: boolean;
}

export function useGetAgencyInsightByUserIdQuery({
  userId,
  initialData,
  enabled,
}: UseGetAgencyInsightByUserIdQueryProps) {
  const fetchApi = useApiRequest<AgencyInsightResponse[]>();
  return useQuery({
    queryKey: ['agency-insight', userId],
    queryFn: () =>
      fetchApi({
        path: `/api/agency-insight/${userId}`,
        method: 'GET',
      }),
    initialData,
    enabled,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}
