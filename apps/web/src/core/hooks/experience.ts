import type { ExperienceResponse } from '@packages/database/types';

import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export const useGetExperiencesByUserIdQuery = (
  userId: string,
  initialData?: ExperienceResponse[]
) => {
  const fetchApi = useApiRequest<ExperienceResponse[]>();
  return useQuery({
    queryKey: ['experience', userId],
    initialData,
    queryFn: () =>
      fetchApi({
        path: `/api/experience/${userId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
};
