import type { UserCertificationResponse } from '@packages/database/types';
import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export const useGetCertificationsByUserIdQuery = (
  userId: string,
  initialData?: UserCertificationResponse[]
) => {
  const fetchApi = useApiRequest<UserCertificationResponse[]>();
  return useQuery({
    queryKey: ['user_certifications', userId],
    initialData: initialData,
    queryFn: () =>
      fetchApi({
        path: `/api/certifications/${userId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
};
