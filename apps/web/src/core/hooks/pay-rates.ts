import type {
  PayRateResponse,
  RateTypeEnumType,
} from '@packages/database/types';

import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export function useGetPayRatesByTypeQuery(rateType: RateTypeEnumType) {
  const fetchApi = useApiRequest<PayRateResponse[]>();
  return useQuery({
    queryKey: ['pay_rates', rateType],
    queryFn: () =>
      fetchApi({
        path: `/api/pay-rates/${rateType}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
