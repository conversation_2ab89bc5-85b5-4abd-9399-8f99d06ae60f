import type { MembersAndInvitationsResponse } from '@packages/database/functions';
import type {
  OrganizationJoinRequestStatusEnumType,
  OrganizationResponse,
} from '@packages/database/types';

import { useQuery } from '@tanstack/react-query';
import { useContext } from 'react';

import OrganizationContext from '@/core/contexts/organization-context';
import { useApiRequest } from '@/core/hooks/use-api';

export const useGetOrganizationWithAddress = (userId: string) => {
  const fetchApi = useApiRequest<OrganizationResponse>();
  return useQuery({
    queryKey: ['organization', userId],
    queryFn: () =>
      fetchApi({ path: `/api/organization/${userId}`, method: 'GET' }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
};

export function useGetMembersData({
  organizationId,
  enabled = true,
}: {
  organizationId: string;
  enabled?: boolean;
}) {
  const fetchApi = useApiRequest<MembersAndInvitationsResponse>();
  return useQuery({
    queryKey: ['members', organizationId],
    queryFn: () =>
      fetchApi({
        path: `/api/organization/members/${organizationId}`,
        method: 'GET',
      }),
    enabled,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export function useGetOrgJoinRequestStatusByUserIdQuery(userId: string) {
  const fetchApi = useApiRequest<{
    status: OrganizationJoinRequestStatusEnumType;
  }>();
  return useQuery({
    queryKey: ['org-join-request', userId],
    queryFn: () =>
      fetchApi({
        path: `/api/organization/join-request-status/${userId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

// -------------------------------------------------------------------
//                          CONTEXT HOOKS
// -------------------------------------------------------------------
export function useCurrentOrganization() {
  const { organization } = useContext(OrganizationContext);
  return organization;
}
