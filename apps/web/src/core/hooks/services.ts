import type {
  ServiceResponse,
  UserServiceResponse,
} from '@packages/database/types';

import { useApiRequest } from '@/core/hooks/use-api';
import { useQuery } from '@tanstack/react-query';

export function useGetServicesQuery() {
  const fetchApi = useApiRequest<ServiceResponse[]>();
  return useQuery({
    queryKey: ['services'],
    queryFn: () =>
      fetchApi({
        path: '/api/services',
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export const useGetServicesByUserIdQuery = ({
  userId,
  initialData,
  enabled,
}: {
  userId: string;
  initialData?: UserServiceResponse[];
  enabled?: boolean;
}) => {
  const fetchApi = useApiRequest<UserServiceResponse[]>();
  return useQuery({
    queryKey: ['services', userId],
    queryFn: () =>
      fetchApi({
        path: `/api/services/${userId}`,
        method: 'GET',
      }),
    initialData: initialData,
    enabled,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
};
