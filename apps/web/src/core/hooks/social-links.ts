import type { SocialLinkResponse } from '@packages/database/types';
import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export function useGetSocialLinksByUserId(userId: string) {
  const fetchApi = useApiRequest<SocialLinkResponse[]>();
  return useQuery({
    queryKey: ['social-links', userId],
    queryFn: () =>
      fetchApi({
        path: `/api/social-links/${userId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
