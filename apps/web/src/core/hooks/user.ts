import type {
  ConsultantProfileCompletion,
  ExpertiseLevelEnumType,
  UserWithAddressResponse,
  UsersResponse,
} from '@packages/database/types';

import { getIsUserClient } from '@packages/supabase/auth-fns/get-is-user-client';
import { useSupabase } from '@packages/supabase/hooks';
import { useQuery } from '@tanstack/react-query';
import { useContext } from 'react';

import UserSessionContext from '@/core/contexts/user-session-context';
import { useApiRequest } from '@/core/hooks/use-api';
import { canInviteUsers } from '@/core/permissions';

export function useUserWithAddressQuery(
  userId: string,
  initialData?: UserWithAddressResponse
) {
  const fetchApi = useApiRequest<UserWithAddressResponse>();
  return useQuery({
    queryKey: ['user-address', userId],
    initialData,
    queryFn: () =>
      fetchApi({
        path: `/api/user/with-address/${userId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export function useUser() {
  const fetchApi = useApiRequest<UsersResponse>();
  return useQuery({
    queryKey: ['user'],
    queryFn: () => fetchApi({ path: '/api/user', method: 'GET' }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export function useGetConsultantProfileCompletion() {
  const fetchApi = useApiRequest<ConsultantProfileCompletion>();
  return useQuery({
    queryKey: ['profile-completion'],
    queryFn: () =>
      fetchApi({ path: '/api/user/profile-completion', method: 'GET' }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export function useIsUsernameAvailableQuery({
  username,
  enabled = true,
}: {
  username: string;
  enabled?: boolean;
}) {
  const fetchApi = useApiRequest<boolean>();
  return useQuery({
    queryKey: ['username-available', username],
    queryFn: () =>
      fetchApi({
        path: `/api/user/availability/${username}`,
        method: 'GET',
      }),
    enabled,
    retry: 1,
    retryDelay: 3000,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

type AuthUserResponse = Omit<UserWithAddressResponse, 'payRateId'> & {
  payRateLabel?: string;
  expertise: {
    id: number;
    skillName: string;
    expertiseLevel: ExpertiseLevelEnumType;
  }[];
};

export function useGetUserForAuthUser(userId: string) {
  const fetchApi = useApiRequest<AuthUserResponse>();
  return useQuery({
    queryKey: ['consultant', userId],
    queryFn: () =>
      fetchApi({
        path: `/api/user/authenticated/${userId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

type PublicUserResponse = Pick<
  UsersResponse,
  'id' | 'avatar' | 'username' | 'firstName' | 'createdAt'
>;

export function useGetUserForPublicUser(userId: string) {
  const fetchApi = useApiRequest<PublicUserResponse>();
  return useQuery({
    queryKey: ['consultant-public', userId],
    queryFn: () =>
      fetchApi({
        path: `/api/user/public/${userId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export function useUserIsClient() {
  const supabase = useSupabase();
  return useQuery({
    queryKey: ['user-is-client'],
    queryFn: async () => {
      return await getIsUserClient(supabase);
    },
  });
}

// -------------------------------------------------------------------
//                          CONTEXT HOOKS
// -------------------------------------------------------------------
export function useUserSession() {
  const { userSession } = useContext(UserSessionContext);
  return userSession;
}

export function useCurrentUserRole() {
  const userSession = useUserSession();
  return userSession?.user?.role;
}

export function useUserCanInviteUsers() {
  const role = useCurrentUserRole();
  return role !== undefined && canInviteUsers(role);
}

export function useUserId() {
  const session = useUserSession();
  return session?.user?.id as string;
}
