import type { SchoolResponseBasic } from '@packages/database/types';
import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export function useGetSchoolsByNameQuery(name: string) {
  const fetchApi = useApiRequest<SchoolResponseBasic[]>();
  return useQuery({
    queryKey: ['schools', name],
    queryFn: () =>
      fetchApi({
        path: `/api/schools/${name}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
}
