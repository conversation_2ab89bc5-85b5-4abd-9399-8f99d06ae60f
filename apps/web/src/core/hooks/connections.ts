import type {
  GetUserConnectionsParams,
  UserConnectionResponse,
} from '@packages/database/functions';

import { useInfiniteQuery, useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

type ConnectionStatusResponse = {
  isCurrentUser: boolean;
  connectionId?: number;
  connectionRequestId?: number;
};

export function useGetConnectionStatusByTargetUserIdQuery(
  targetUserId: string
) {
  const fetchApi = useApiRequest<ConnectionStatusResponse>();
  return useQuery({
    queryKey: ['connection-status', targetUserId],
    queryFn: () =>
      fetchApi({
        path: `/api/connections/status/${targetUserId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

type QueryProps = Omit<GetUserConnectionsParams, 'page' | 'userId'>;

export function useGetUserConnectionsQuery({ ...params }: QueryProps) {
  const fetchApi = useApiRequest<UserConnectionResponse[]>();
  return useInfiniteQuery({
    queryKey: ['connections', params.sortBy, params.isAscending],
    queryFn: ({ pageParam = 1 }) =>
      fetchApi({
        path: `/api/connections?sortBy=${params.sortBy}&isAscending=${params.isAscending}&page=${pageParam}&perPage=${params.perPage}`,
        method: 'GET',
      }).then((connections) => ({
        connections,
        nextPage:
          connections.length === params.perPage ? pageParam + 1 : undefined,
      })),
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 1,
    staleTime: 5 * 60 * 1000,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
    retryDelay: 3000,
    retry: 1,
  });
}
