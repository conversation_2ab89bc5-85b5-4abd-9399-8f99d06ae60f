import type {
  GetUserChatRoomsParams,
  UserChatRoomsResponse,
} from '@packages/database/functions';

import { useApiRequest } from '@/core/hooks/use-api';
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';

type QueryProps = Omit<GetUserChatRoomsParams, 'page' | 'userId'>;

export function useGetChatRooms({ ...params }: QueryProps) {
  const fetchApi = useApiRequest<UserChatRoomsResponse[]>();
  return useInfiniteQuery({
    queryKey: ['chat-rooms', params],
    queryFn: async ({ pageParam: page = 1 }) =>
      fetchApi({
        path: `/api/chat/rooms?page=${page}&perPage=${params.perPage}&withMessagesOnly=${params.withMessagesOnly}&isArchived=${params.isArchived}&searchName=${params.searchName}&sortByName=${params.sortByName}`,
        method: 'GET',
      }).then((chatRooms) => ({
        chatRooms,
        nextPage: chatRooms.length === params.perPage ? page + 1 : undefined,
      })),
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 1,
    retry: 1,
    retryDelay: 3000,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export function useGetChatRoomIdForUsers(targetUserId: string) {
  const fetchApi = useApiRequest<string>();
  return useQuery({
    queryKey: ['chat-room-id-for-users', targetUserId],
    queryFn: () =>
      fetchApi({
        path: `/api/chat/room-id/${targetUserId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
