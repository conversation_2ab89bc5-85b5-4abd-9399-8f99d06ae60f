import type { EducationResponse } from '@packages/database/types';

import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export const useGetEducationByUserIdQuery = (
  userId: string,
  initialData?: EducationResponse[]
) => {
  const fetchApi = useApiRequest<EducationResponse[]>();
  return useQuery({
    queryKey: ['education', userId],
    initialData: initialData,
    queryFn: () =>
      fetchApi({
        path: `/api/education/${userId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
};
