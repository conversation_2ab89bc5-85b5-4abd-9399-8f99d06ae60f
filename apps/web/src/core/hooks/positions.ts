import type { PositionResponse } from '@packages/database/types';
import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export function useGetPositionsByName(name: string) {
  const fetchApi = useApiRequest<PositionResponse[]>();
  return useQuery({
    queryKey: ['positions', name],
    queryFn: () => fetchApi({ path: `/api/positions/${name}`, method: 'GET' }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
