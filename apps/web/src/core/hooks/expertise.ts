import type { UserSkillResponse } from '@packages/database/types';

import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';

export const useGetExpertiseByUserIdQuery = (
  userId: string,
  initialData?: UserSkillResponse[]
) => {
  const fetchApi = useApiRequest<UserSkillResponse[]>();
  return useQuery({
    queryKey: ['expertise', userId],
    initialData: initialData,
    queryFn: () =>
      fetchApi({
        path: `/api/expertise/${userId}`,
        method: 'GET',
      }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
};
