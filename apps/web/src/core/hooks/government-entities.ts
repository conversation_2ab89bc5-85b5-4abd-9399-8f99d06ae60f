import {
  type GovernmentEntityResponse,
  GovernmentEntityType,
} from '@packages/database/types';

import { useQuery } from '@tanstack/react-query';

import { useApiRequest } from '@/core/hooks/use-api';
import { useMemo } from 'react';

export function useGetGovernmentEntities(
  entityType: GovernmentEntityType,
  name: string
) {
  const fetchApi = useApiRequest<GovernmentEntityResponse[]>();

  const path = useMemo(() => {
    switch (entityType) {
      case GovernmentEntityType.DEPARTMENT:
        return `/api/government/departments/${name}`;
      case GovernmentEntityType.AGENCY:
        return `/api/government/agencies/${name}`;
      case GovernmentEntityType.OFFICE:
        return `/api/government/offices/${name}`;
      case GovernmentEntityType.SUBOFFICE:
        return `/api/government/sub-offices/${name}`;
      default:
        return '';
    }
  }, [entityType, name]);

  return useQuery({
    queryKey: ['government-entities', entityType, name],
    queryFn: () => fetchApi({ path, method: 'GET' }),
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
