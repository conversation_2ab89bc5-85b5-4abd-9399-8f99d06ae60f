'use client';

import type { ConnectionRequestResponse } from '@packages/database/types';
import type { ConnectionRequestDao } from '@packages/supabase/dao';

import { useSupabase } from '@packages/supabase/hooks';
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { useContext, useEffect } from 'react';
import { toast } from 'sonner';

import AppContainerContext from '@/core/contexts/app-container-context';
import { useApiRequest } from '@/core/hooks/use-api';

const CONNECTION_REQUEST_TABLE = 'connection_request';

type Response = { requests: ConnectionRequestResponse[]; total: number };

export function useGetConnectionRequestsByUserIdQuery({
  userId,
  perPage,
}: {
  userId: string;
  perPage?: number;
}) {
  const fetchApi = useApiRequest<Response>();
  return useInfiniteQuery({
    queryKey: ['connection-requests', userId],
    queryFn: async ({ pageParam = 1 }) =>
      fetchApi({
        path: `/api/connection-requests/${userId}?page=${pageParam}&perPage=${perPage}`,
        method: 'GET',
      })
        .then((res) => ({
          connectionRequests: res?.requests || [],
          nextPage:
            res?.requests?.length === perPage ? pageParam + 1 : undefined,
          total: res?.total || 0,
        }))
        .catch((error) => {
          toast.error('Error loading connection requests');
          throw error;
        }),
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 1,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    retry: 1,
    retryDelay: 3000,
    staleTime: 5 * 60 * 1000,
  });
}

export function useGetSentConnectionRequestsByUserIdQuery({
  userId,
  perPage,
}: {
  userId: string;
  perPage?: number;
}) {
  const fetchApi = useApiRequest<Response>();
  return useInfiniteQuery({
    queryKey: ['sent-connection-requests', userId],
    queryFn: async ({ pageParam = 1 }) =>
      fetchApi({
        path: `/api/connection-requests/sent/${userId}?page=${pageParam}&perPage=${perPage}`,
        method: 'GET',
      })
        .then((res) => ({
          connectionRequests: res?.requests || [],
          nextPage:
            res?.requests?.length === perPage ? pageParam + 1 : undefined,
          total: res?.total || 0,
        }))
        .catch((error) => {
          toast.error('Error loading sent connection requests');
          throw error;
        }),
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 1,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    retry: 1,
    retryDelay: 3000,
    staleTime: 5 * 60 * 1000,
  });
}

export function useGetConnectionRequestsCountQuery({
  userId,
}: {
  userId: string;
}) {
  const fetchApi = useApiRequest<{ read: number; unread: number }>();
  return useQuery({
    queryKey: ['connection-requests-count', userId],
    queryFn: () =>
      fetchApi({
        path: `/api/connection-requests/count?userId=${userId}`,
        method: 'GET',
      }),
    retry: 1,
    retryDelay: 3000,
    staleTime: 5 * 60 * 1000,
  });
}

export function useStreamConnectionRequestsCount({
  userId,
}: { userId: string }) {
  const supabase = useSupabase();
  const { setConnectionRequestsCount } = useContext(AppContainerContext);

  useEffect(() => {
    if (!userId) {
      console.error('User not found.');
      return;
    }

    const channel = supabase.channel('connection_request');

    channel
      .on<ConnectionRequestDao>(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: CONNECTION_REQUEST_TABLE,
          filter: `receiver_id=eq.${userId}`,
        },
        (payload) => {
          const event = payload.eventType;
          if (event === 'INSERT') {
            setConnectionRequestsCount(({ read, unread }) => ({
              read,
              unread: unread + 1,
            }));
          } else if (event === 'UPDATE') {
            if (!!payload.new.is_read) {
              setConnectionRequestsCount(({ read, unread }) => ({
                read: read + 1,
                unread: unread - 1,
              }));
            }
          } else if (event === 'DELETE') {
            setConnectionRequestsCount(({ read, unread }) => ({
              read,
              unread: Math.max(0, unread - 1),
            }));
          }
        }
      )
      .subscribe();

    return () => {
      void channel.unsubscribe();
    };
  }, [setConnectionRequestsCount, supabase, userId]);
}
