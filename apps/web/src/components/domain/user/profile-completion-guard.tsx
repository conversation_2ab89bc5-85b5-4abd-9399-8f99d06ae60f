'use client';

import { Slot } from '@packages/ui/button';
import { usePathname } from 'next/navigation';
import { useCallback, useContext, useRef } from 'react';
import { toast } from 'sonner';

import { SiteConfig } from '@/configuration';
import AppContainerContext from '@/core/contexts/app-container-context';
import {
  useGetConsultantProfileCompletion,
  useUserSession,
} from '@/core/hooks/user';

export function ProfileCompletionGuard({ children }: React.PropsWithChildren) {
  const pathname = usePathname();
  const session = useUserSession();
  const { data: completion } = useGetConsultantProfileCompletion();
  const { triggerProfileCompletionDockShake } = useContext(AppContainerContext);

  const isClient = session?.user?.isClient === true;

  const lastCallTimeRef = useRef<number>(0);
  const debounceDelay = 1000;

  const handleProfileCompletionClick = useCallback(() => {
    const now = Date.now();
    if (now - lastCallTimeRef.current < debounceDelay) {
      return;
    }
    lastCallTimeRef.current = now;

    if (
      pathname.startsWith(SiteConfig.paths.settings.profile) ||
      !pathname.startsWith(SiteConfig.paths.app.root)
    ) {
      toast.error('Please complete your profile.');
      return;
    }

    if (completion?.isComplete) {
      return;
    }

    triggerProfileCompletionDockShake();
  }, [pathname, completion?.isComplete, triggerProfileCompletionDockShake]);

  if (completion?.isComplete || isClient) {
    return children;
  }

  return <Slot onClick={handleProfileCompletionClick}>{children}</Slot>;
}
