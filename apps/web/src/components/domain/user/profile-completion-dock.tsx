'use client';

import type { ConsultantProfileCompletion } from '@packages/database/types';

import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { AnimatePresence, motion } from 'framer-motion';
import { usePathname } from 'next/navigation';
import { useContext, useState } from 'react';

import { ShakeAnimation } from '@packages/ui/animations';
import { BorderBeam } from '@packages/ui/border-beam';
import { Button } from '@packages/ui/button';
import { cn } from '@packages/utils/cn';

import { SiteConfig } from '@/configuration';
import AppContainerContext from '@/core/contexts/app-container-context';
import { useGetConsultantProfileCompletion } from '@/core/hooks/user';
import { ProfileCompletionItem } from './profile-completion-item';

export function ProfileCompletionDock() {
  const pathname = usePathname();
  const isProfileSettingsPage = pathname.startsWith(
    SiteConfig.paths.settings.profile
  );

  const [isCollapsed, setIsCollapsed] = useState(false);

  const { isProfileCompletionDockShaking } = useContext(AppContainerContext);
  const { data: completion, isPending } = useGetConsultantProfileCompletion();

  if (isPending || completion?.isComplete || isProfileSettingsPage) {
    return null;
  }

  const items = getSections(completion!);
  const progress = items
    .filter((s) => s.isComplete)
    .reduce((sum, section) => sum + section.points, 0);

  const toggleCollapse = () => setIsCollapsed((prev) => !prev);

  return (
    <div className="fixed bottom-20 md:bottom-5 inset-x-0 px-4 z-30">
      <ShakeAnimation shake={isProfileCompletionDockShaking}>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="relative w-full mx-auto max-w-xl border border-border rounded-xl overflow-hidden transition-opacity duration-300"
        >
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                className="bg-background/50 backdrop-blur-md space-y-2 p-4"
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
              >
                {items.map((item, index) => (
                  <motion.div
                    key={item.label}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05, duration: 0.2 }}
                  >
                    <ProfileCompletionItem
                      label={item.label}
                      path={item.path}
                      isComplete={item.isComplete}
                    />
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>

          <div
            onClick={toggleCollapse}
            className={cn(
              'relative flex justify-between items-center p-4 border border-border bg-background/50 backdrop-blur-md cursor-pointer rounded-xl overflow-hidden'
            )}
          >
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 1, ease: 'easeOut' }}
              style={{ width: `${progress}%` }}
              className={cn(
                'absolute inset-0 pointer-events-none overflow-hidden bg-gradient-to-r transition-all duration-500 ease-in-out',
                {
                  'from-red-200/40 to-red-500/40': progress < 30,
                  'from-yellow-200/40 to-yellow-500/40':
                    progress >= 30 && progress < 70,
                  'from-green-200/40 to-green-500/40': progress >= 70,
                }
              )}
            >
              <div className="absolute inset-0 animate-shine">
                <div className="h-full w-1/3 bg-gradient-to-r from-transparent via-white/20 to-transparent" />
              </div>
            </motion.div>

            <div className="flex items-center gap-3 z-[31]">
              <span
                className={cn('text-4xl font-bold', {
                  'text-red-500': progress < 30,
                  'text-yellow-500': progress >= 30 && progress < 70,
                  'text-green-500': progress >= 70,
                })}
              >
                {progress}%
              </span>
              <div className="flex flex-col">
                <span className="text-base text-foreground font-medium">
                  Your profile is hidden.
                </span>
                <span className="hidden xs:inline text-sm text-muted-foreground">
                  Complete your profile to be visible to clients.
                </span>
              </div>
            </div>

            <Button size="icon" className="size-10 rounded-full z-[31]">
              {isCollapsed ? (
                <ChevronUpIcon className="size-5 text-background" />
              ) : (
                <ChevronDownIcon className="size-5 text-background" />
              )}
            </Button>

            <BorderBeam
              size={100}
              className={cn({
                'from-red-300 via-red-500': progress < 30,
                'from-yellow-300 via-yellow-500':
                  progress >= 30 && progress < 70,
                'from-green-300 via-green-500': progress >= 70,
              })}
            />
          </div>
        </motion.div>
      </ShakeAnimation>
    </div>
  );
}

function getSections(profileCompletion: ConsultantProfileCompletion) {
  const profilePath = SiteConfig.paths.settings.profile;
  return [
    {
      label: 'Basic Information',
      path: profilePath,
      points: 10,
      isComplete: profileCompletion.basicInfo,
    },
    {
      label: 'Profile Picture',
      path: profilePath,
      points: 5,
      isComplete: profileCompletion.profilePicture,
    },
    {
      label: 'Work Experience',
      path: `${profilePath}/work-experience`,
      points: 20,
      isComplete: profileCompletion.workExperience,
    },
    {
      label: 'Past Performance',
      path: `${profilePath}/past-performance`,
      points: 10,
      isComplete: profileCompletion.pastPerformance,
    },
    {
      label: 'Education',
      path: `${profilePath}/education`,
      points: 20,
      isComplete: profileCompletion.education,
    },
    {
      label: 'Expertise (minimum 3)',
      path: `${profilePath}/expertise`,
      points: 15,
      isComplete: profileCompletion.expertise,
    },
    {
      label: 'Services',
      path: `${profilePath}/services`,
      points: 20,
      isComplete: profileCompletion.services,
    },
  ];
}
