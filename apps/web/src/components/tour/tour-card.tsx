'use client';

import type { CardComponentProps } from 'onborda';

import { XMarkIcon } from '@heroicons/react/24/outline';
import confetti from 'canvas-confetti';
import { useRouter } from 'next/navigation';
import { useOnborda } from 'onborda';

import { updateUserTourCompletionAction } from '@/core/server/actions/tour-completion';
import { Button } from '@packages/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@packages/ui/card';

import { SiteConfig } from '@/configuration';
import { useMediaQuery } from '@uidotdev/usehooks';
import { useEffect } from 'react';

export const TourCard: React.FC<CardComponentProps> = ({
  step,
  currentStep,
  totalSteps,
  nextStep,
  prevStep,
  arrow,
}) => {
  const { setCurrentStep, closeOnborda } = useOnborda();
  const isMobile = useMediaQuery('(max-width: 768px)');
  const router = useRouter();

  const handleRestartTour = () => {
    setCurrentStep(0);
    router.push(SiteConfig.paths.app.dashboard);
  };

  const handleSkipTour = () => {
    closeOnborda();
    updateUserTourCompletionAction({ completed: true });
  };

  const handleFinishTour = () => {
    closeOnborda();
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 },
    });
    updateUserTourCompletionAction({ completed: true });
  };

  useEffect(() => {
    if (isMobile) {
      closeOnborda();
    }
  }, [isMobile]);

  return (
    <Card className="relative border-0 min-w-80 z-[999] bg-muted border-none">
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between w-full space-x-4">
          <div className="flex flex-col space-y-2">
            <CardDescription className="text-foreground/50">
              {currentStep + 1} of {totalSteps}
            </CardDescription>
            <CardTitle className="mb-2 text-lg text-foreground space-x-2">
              <span>{step.icon}</span>
              <span>{step.title}</span>
            </CardTitle>
          </div>
          <Button
            variant="ghost"
            className="text-foreground/50 absolute top-4 right-2 hover:bg-transparent hover:text-foreground/80"
            onClick={handleSkipTour}
            size="icon"
          >
            <XMarkIcon className="size-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="text-foreground text-sm">
        {step.content}
      </CardContent>
      <CardFooter className="text-foreground">
        <div className="flex justify-between w-full gap-4">
          {currentStep !== 0 && (
            <Button
              variant="outline"
              onClick={() => prevStep()}
              className="dark:border-primary dark:hover:bg-primary/10 dark:hover:text-primary"
            >
              Previous
            </Button>
          )}
          {currentStep === 0 && (
            <Button
              variant="outline"
              onClick={handleSkipTour}
              className="dark:border-primary dark:hover:bg-primary/10 dark:hover:text-primary"
            >
              Skip Tour
            </Button>
          )}
          {currentStep + 1 !== totalSteps && (
            <Button onClick={() => nextStep()} className="ml-auto">
              Next
            </Button>
          )}
          {currentStep + 1 === totalSteps && (
            <>
              <Button className="ml-auto" onClick={handleRestartTour}>
                Restart Tour
              </Button>
              <Button className="ml-auto" onClick={handleFinishTour}>
                🎉 Finish!
              </Button>
            </>
          )}
        </div>
      </CardFooter>
      <span className="text-muted">{arrow}</span>
    </Card>
  );
};
