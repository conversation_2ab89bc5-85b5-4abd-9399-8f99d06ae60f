'use client';

import { motion } from 'framer-motion';
import { useOnborda } from 'onborda';
import { useContext, useState } from 'react';

import { MembershipRole } from '@packages/database/enums';
import { Button } from '@packages/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@packages/ui/dialog';
import { Logo } from '@packages/ui/logo';
import { VisuallyHidden } from '@packages/ui/visually-hidden';

import { useUserSession } from '@/core/hooks/user';
import { updateUserTourCompletionAction } from '@/core/server/actions/tour-completion';

export function TourModal() {
  const userSession = useUserSession();
  const { startOnborda } = useOnborda();

  const [isOpen, setIsOpen] = useState(true);

  const { isClient, role: userRole } = userSession?.user!;

  const handleStartTour = () => {
    const isOwner = userRole === MembershipRole.Owner;
    const tourName = isClient
      ? isOwner
        ? 'client-owner-tour'
        : 'client-tour'
      : 'consultant-tour';

    setIsOpen(false);
    setTimeout(() => {
      startOnborda(tourName);
    }, 50);
  };

  const handleSkip = () => {
    setIsOpen(false);
    updateUserTourCompletionAction({ completed: true });
  };

  const onOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      updateUserTourCompletionAction({ completed: true });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] p-0 gap-0 overflow-hidden">
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent" />
          <div className="absolute top-0 right-0 size-52 bg-primary/5 rounded-full -translate-y-1/2 translate-x-1/2" />
          <div className="absolute bottom-0 left-0 size-52 bg-primary/5 rounded-full translate-y-1/2 -translate-x-1/2" />

          <div className="relative px-8 py-12 flex flex-col items-center text-center space-y-6">
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="flex items-center"
            >
              <Logo icon className="h-10 w-12 fill-current" />
              <Logo className="mt-1 h-10 w-32 fill-current" />
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="space-y-4"
            >
              <DialogTitle className="text-2xl font-bold tracking-tight">
                Welcome to GovMaven
              </DialogTitle>
              <DialogDescription className="text-muted-foreground max-w-md mx-auto">
                Your gateway to the U.S. government's work marketplace. Let's
                take a quick tour to help you get started and make the most of
                your experience.
              </DialogDescription>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="flex flex-col sm:flex-row gap-3 w-full max-w-md"
            >
              <Button
                variant="outline"
                className="bg-background flex-1"
                onClick={handleSkip}
              >
                Maybe Later
              </Button>
              <Button className="flex-1" onClick={handleStartTour}>
                Start a Tour
              </Button>
            </motion.div>

            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="text-xs text-muted-foreground"
            >
              You can always restart the tour later using the help button in the
              sidebar
            </motion.p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
