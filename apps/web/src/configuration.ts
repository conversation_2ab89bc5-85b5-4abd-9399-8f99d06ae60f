import type { Provider } from '@packages/supabase/types';

const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;
const isProduction = environment === 'production';

export const SiteConfig = {
  site: {
    name: 'GovMaven',
    description: "The U.S. Government's Work Marketplace",
    url: process.env.NEXT_PUBLIC_SITE_URL!,
    email: '<EMAIL>',
    twitterUrl: 'https://x.com/govmaven',
    twitterHandle: '@govmaven',
  },
  ably: {
    channelPrefix: 'chat-govmaven:',
  },
  auth: {
    providers: {
      emailPassword: true,
      phoneNumber: false,
      emailLink: false,
      emailOtp: false,
      oAuth: ['google'] as Provider[],
    },
  },
  captcha: {
    siteKey: process.env.NEXT_PUBLIC_CAPTCHA_SITE_KEY!,
  },
  isProduction,
  environment,
  features: {
    enableAccountDeletion: getBoolean(
      process.env.NEXT_PUBLIC_ENABLE_ACCOUNT_DELETION,
      false
    ),
    enableOrganizationDeletion: getBoolean(
      process.env.NEXT_PUBLIC_ENABLE_ORGANIZATION_DELETION,
      false
    ),
  },
  paths: {
    root: '/',
    app: {
      root: '/app',
      search: '/app/search',
      messages: '/app/messages',
      dashboard: '/app/dashboard',
      settings: '/app/settings',
      network: '/app/network',
    },
    auth: {
      root: '/auth',
      signIn: '/auth/sign-in',
      signUp: '/auth/sign-up',
      verifyMfa: '/auth/verify',
      resetPassword: '/auth/reset-password',
      resetPasswordRequest: '/auth/reset-password-request',
      callback: '/auth/callback',
    },
    settings: {
      root: '/app/settings',
      profile: '/app/settings/profile',
      security: '/app/settings/security',
      notifications: '/app/settings/notifications',
      reviews: '/app/settings/reviews',
      preferences: '/app/settings/preferences',
      organization: '/app/settings/organization',
      employees: '/app/settings/employees',
      invitations: '/app/settings/invitations',
      organizationRequests: '/app/settings/organization-requests',
      billing: '/app/settings/billing',
    },
    network: {
      root: '/app/network',
      sentRequests: '/app/network/sent-requests',
      connections: '/app/network/connections',
    },
    admin: {
      root: '/admin',
      users: '/admin/users',
      organizations: '/admin/organizations',
    },
    checkout: '/checkout',
    invite: '/invite',
    onboarding: '/onboarding',
    review: '/review',
    legal: {
      root: '/legal',
      privacy: '/legal/privacy',
      terms: '/legal/terms',
      consultantsTerms: '/legal/terms/consultants',
      clientsTerms: '/legal/terms/clients',
    },
    marketing: {
      about: { title: 'About', href: '/about' },
      pricing: { title: 'Pricing', href: '/pricing' },
      mavens: { title: 'Mavens', href: '/mavens' },
      companies: { title: 'Companies', href: '/companies' },
    },
  },
  sentry: {
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  },
};

export interface StripeConfigType {
  plans: {
    name: string;
    badge?: string;
    description: string;
    recommended: boolean;
    features: string[];
    stripePriceId?: string;
    price?: number;
    unitPrice?: number;
    minSeats?: number;
    maxSeats?: number;
  }[];
}

export const StripeConfig: StripeConfigType = {
  plans: [
    {
      name: 'Basic',
      badge: 'Up to 7 users',
      description:
        'The ultimate small-business package — everything you need to get started finding the best SMEs.',
      recommended: false,
      features: [
        'Basic Reporting',
        'Up to 7 Users',
        'Chat Support',
        '+$165/month per additional user',
      ],
      stripePriceId: process.env.STRIPE_BASIC_PRICE_ID,
      price: 495,
      unitPrice: 165, // per month
      minSeats: 3,
      maxSeats: 7,
    },
    {
      name: 'Premium',
      badge: 'Most Popular',
      description:
        'Great for large organizations who need more seats to manage and maximize their consultant network.',
      recommended: true,
      features: [
        'Advanced Reporting',
        'Unlimited Users',
        'Chat and Phone Support',
        '+$125/month per additional user',
      ],
      stripePriceId: process.env.STRIPE_PREMIUM_PRICE_ID,
      price: 1250, // $1250 / 12 = 104.16 / Month
      unitPrice: 125, // per month
      minSeats: 10,
      maxSeats: Number.POSITIVE_INFINITY,
    },
    {
      name: 'Enterprise',
      description:
        'The ultimate plan for large enterprises seeking unparalleled functionality and dedicated support.',
      recommended: false,
      features: [
        'Advanced Reporting',
        'Unlimited Users',
        'Dedicated Account Manager(s)',
      ],
    },
  ],
};

// Validate Stripe configuration
// as this is a new requirement, we throw an error if the key is not defined
// in the environment
if (isProduction && !process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
  throw new Error(
    'The key NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is not defined. Please add it to your environment variables.'
  );
}

function getBoolean(value: unknown, defaultValue: boolean) {
  if (typeof value === 'string') {
    return value === 'true';
  }

  return defaultValue;
}
