export enum CacheKey {
  OnboardingData = 'onboarding-data',
}

const PREFIX = 'govmaven';

function joinKeyParts(...parts: string[]): string[] {
  return parts.filter((part) => part.length > 0);
}

function joinTagParts(...parts: string[]): string {
  return parts.filter((part) => part.length > 0).join(':');
}

export class Caching {
  public static createKeyParts(
    key: <PERSON><PERSON><PERSON><PERSON>,
    userId: string,
    ...additionalKeyParts: string[]
  ): string[] {
    if (!userId) {
      throw new Error('User ID cannot be empty');
    }
    return joinKeyParts(
      PREFIX,
      userId,
      key.toLowerCase(),
      ...additionalKeyParts
    );
  }

  public static createTag(
    key: <PERSON><PERSON><PERSON><PERSON>,
    userId: string,
    ...additionalTagParts: string[]
  ): string {
    if (!userId) {
      throw new Error('User ID cannot be empty');
    }
    return joinTagParts(
      PREFIX,
      userId,
      key.toLowerCase(),
      ...additionalTagParts
    );
  }
}

export const defaultRevalidateTimeInSeconds =
  process.env.ENVIRONMENT === 'production' ? 3600 : 120;
