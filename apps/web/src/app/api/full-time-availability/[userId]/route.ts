import type { FullTimeAvailabilityResponse } from '@packages/database/types';

import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  fullTimeAvailabilityTable,
  payRateTable,
  positionTable,
} from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const userFullTimeAvailability: FullTimeAvailabilityResponse[] =
    await db.transaction(async (tx) => {
      return await tx
        .select({
          id: fullTimeAvailabilityTable.id,
          locationType: fullTimeAvailabilityTable.locationType,
          desiredStartDate: fullTimeAvailabilityTable.desiredStartDate,
          position: {
            id: positionTable.id,
            name: positionTable.name,
          },
          payRate: {
            id: payRateTable.id,
            label: payRateTable.label,
          },
        })
        .from(fullTimeAvailabilityTable)
        .innerJoin(
          positionTable,
          eq(positionTable.id, fullTimeAvailabilityTable.positionId)
        )
        .innerJoin(
          payRateTable,
          eq(payRateTable.id, fullTimeAvailabilityTable.payRateId)
        )
        .where(eq(fullTimeAvailabilityTable.userId, parsedUserId));
    });

  if (!userFullTimeAvailability) {
    logger.error('❌ FULL TIME AVAILABILITY NOT FOUND');
    return Response.json(
      { error: 'FULL_TIME_AVAILABILITY_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(userFullTimeAvailability);
});
