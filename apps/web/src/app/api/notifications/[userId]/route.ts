import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { and, desc, eq, gt } from '@packages/database/drizzle-orm';
import { notificationTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const notifications = await db.transaction(async (tx) => {
    return await tx
      .select({
        id: notificationTable.id,
        type: notificationTable.type,
        channel: notificationTable.channel,
        content: notificationTable.content,
        metadata: notificationTable.metadata,
        userId: notificationTable.userId,
        isProcessed: notificationTable.isProcessed,
        isDismissed: notificationTable.isDismissed,
        dismissedAt: notificationTable.dismissedAt,
        expiresAt: notificationTable.expiresAt,
        createdAt: notificationTable.createdAt,
      })
      .from(notificationTable)
      .where(
        and(
          eq(notificationTable.userId, parsedUserId),
          eq(notificationTable.channel, 'in_app'),
          gt(notificationTable.expiresAt, new Date())
        )
      )
      .orderBy(desc(notificationTable.createdAt));
  });

  if (!notifications) {
    logger.error('❌ NOTIFICATIONS NOT FOUND');
    return Response.json(
      { error: 'NOTIFICATIONS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(notifications);
});
