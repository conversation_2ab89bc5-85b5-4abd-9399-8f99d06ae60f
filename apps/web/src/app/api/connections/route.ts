import type { NextRequest } from 'next/server';

import {
  createSearchParamsCache,
  parseAsBoolean,
  parseAsInteger,
  parseAsStringEnum,
} from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { getUserConnections } from '@packages/database/functions';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';

const searchParamsCache = createSearchParamsCache({
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  isAscending: parseAsBoolean.withDefault(false),
  sortBy: parseAsStringEnum([
    'first_name',
    'last_name',
    'connected_at',
  ]).withDefault('connected_at'),
});

export const GET = withAuth(async (req: NextRequest, ctx) => {
  const searchParams = req.nextUrl.searchParams;
  const parsedSearchParams = searchParamsCache.parse(
    Object.fromEntries(searchParams.entries())
  );

  const userId = ctx.authUser.id;
  const db = await createDrizzleSupabaseClient();

  const connections = await getUserConnections(db, {
    ...parsedSearchParams,
    userId,
  });

  if (!connections) {
    logger.error('❌ CONNECTIONS NOT FOUND');
    return Response.json(
      { error: 'CONNECTIONS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(connections);
});
