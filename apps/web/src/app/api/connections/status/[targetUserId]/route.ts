import { createDrizzleSupabaseClient } from '@packages/database';
import { getConnectionStatus } from '@packages/database/functions';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';
import { parseAsUuid } from '@/utils/nuqs-parser';

export const GET = withAuth(async (_req, ctx) => {
  const { targetUserId } = await ctx.params;
  const parsedTargetUserId = parseAsUuid.parseServerSide(targetUserId);

  if (!parsedTargetUserId) {
    logger.error('❌ MISSING TARGET USER ID');
    return Response.json(
      { error: 'MISSING_TARGET_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const userId = ctx.authUser.id;
  const db = await createDrizzleSupabaseClient();

  const connectionStatus = await getConnectionStatus(db, {
    targetUserId: parsedTargetUserId,
    currentUserId: userId,
  });

  const result = {
    ...connectionStatus,
    isCurrentUser: userId === parsedTargetUserId,
  };

  return Response.json(result);
});
