import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { organizationJoinRequestTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';
import { parseAsUuid } from '@/utils/nuqs-parser';

export const GET = withAuth(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const [orgJoinRequest] = await db.transaction(async (tx) => {
    return await tx
      .select({ status: organizationJoinRequestTable.status })
      .from(organizationJoinRequestTable)
      .where(eq(organizationJoinRequestTable.userId, parsedUserId))
      .limit(1);
  });

  if (!orgJoinRequest) {
    logger.error('❌ ORG JOIN REQUEST NOT FOUND');
    return Response.json(
      { error: 'ORG_JOIN_REQUEST_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(orgJoinRequest);
});
