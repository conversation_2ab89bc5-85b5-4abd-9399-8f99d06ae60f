import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  addressTable,
  membershipTable,
  organizationAddressTable,
  organizationTable,
} from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';

export const GET = withAuth(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const [organization] = await db.transaction(async (tx) => {
    return await tx
      .select({
        id: organizationTable.id,
        ein: organizationTable.ein,
        name: organizationTable.name,
        logo: organizationTable.logo,
        description: organizationTable.description,
        cageCode: organizationTable.cageCode,
        primaryNaicsCode: organizationTable.primaryNaicsCode,
        address: {
          id: addressTable.id,
          address1: addressTable.address1,
          address2: addressTable.address2,
          city: addressTable.city,
          state: addressTable.state,
          zipCode: addressTable.zipCode,
        },
      })
      .from(membershipTable)
      .innerJoin(
        organizationTable,
        eq(membershipTable.organizationId, organizationTable.id)
      )
      .leftJoin(
        organizationAddressTable,
        eq(organizationAddressTable.organizationId, organizationTable.id)
      )
      .leftJoin(
        addressTable,
        eq(addressTable.id, organizationAddressTable.addressId)
      )
      .where(eq(membershipTable.userId, parsedUserId))
      .limit(1);
  });

  if (!organization) {
    logger.error('❌ ORGANIZATION NOT FOUND');
    return Response.json(
      { error: 'ORGANIZATION_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(organization);
});
