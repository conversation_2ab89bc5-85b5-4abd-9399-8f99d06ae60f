import { createDrizzleSupabaseClient } from '@packages/database';
import { getMembersAndInvitationByOrgId } from '@packages/database/functions';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';
import { parseAsUuid } from '@/utils/nuqs-parser';

export const GET = withAuth(async (_req, ctx) => {
  const { organizationId } = await ctx.params;
  const parsedOrganizationId = parseAsUuid.parseServerSide(organizationId);

  if (!parsedOrganizationId) {
    logger.error('❌ MISSING ORGANIZATION ID');
    return Response.json(
      { error: 'MISSING_ORGANIZATION_ID' },
      {
        status: 400,
      }
    );
  }

  const isClient = ctx.authUser.app_metadata.isClient === true;
  if (!isClient) {
    logger.error('❌ USER IS NOT A CLIENT');
    return Response.json(
      { error: 'USER_IS_NOT_A_CLIENT' },
      {
        status: 403,
      }
    );
  }

  const dbAdmin = await createDrizzleSupabaseClient({ admin: true });

  const members = await getMembersAndInvitationByOrgId(dbAdmin, {
    organizationId: parsedOrganizationId,
  });

  if (!members) {
    logger.error('❌ MEMBERS NOT FOUND');
    return Response.json(
      { error: 'MEMBERS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(members);
});
