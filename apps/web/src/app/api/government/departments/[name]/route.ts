import type { GovernmentEntityResponse } from '@packages/database/types';
import { parseAsString } from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { ilike } from '@packages/database/drizzle-orm';
import { governmentDepartmentTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { name } = await ctx.params;
  const parsedName = parseAsString.parseServerSide(name);

  if (!parsedName) {
    logger.error('❌ MISSING GOVERNMENT DEPARTMENT NAME');
    return Response.json(
      { error: 'MISSING_GOVERNMENT_DEPARTMENT_NAME' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const governmentDepartments: GovernmentEntityResponse[] =
    await db.transaction(async (tx) => {
      return await tx
        .select({
          id: governmentDepartmentTable.id,
          name: governmentDepartmentTable.name,
        })
        .from(governmentDepartmentTable)
        .where(ilike(governmentDepartmentTable.name, `%${parsedName}%`))
        .limit(50);
    });

  if (!governmentDepartments) {
    logger.error('❌ GOVERNMENT DEPARTMENTS NOT FOUND');
    return Response.json(
      { error: 'GOVERNMENT_DEPARTMENTS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(governmentDepartments);
});
