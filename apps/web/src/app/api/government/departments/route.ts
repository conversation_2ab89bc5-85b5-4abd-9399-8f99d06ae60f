import type { GovernmentEntityResponse } from '@packages/database/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { governmentDepartmentTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, _ctx) => {
  const db = await createDrizzleSupabaseClient();

  const governmentDepartments: GovernmentEntityResponse[] =
    await db.transaction(async (tx) => {
      return await tx
        .select({
          id: governmentDepartmentTable.id,
          name: governmentDepartmentTable.name,
        })
        .from(governmentDepartmentTable)
        .limit(50);
    });

  if (!governmentDepartments) {
    logger.error('❌ GOVERNMENT DEPARTMENTS NOT FOUND');
    return Response.json(
      { error: 'GOVERNMENT_DEPARTMENTS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(governmentDepartments);
});
