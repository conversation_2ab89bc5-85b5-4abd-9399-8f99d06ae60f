import type { GovernmentEntityResponse } from '@packages/database/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  governmentAgencyTable,
  governmentDepartmentTable,
} from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, _ctx) => {
  const db = await createDrizzleSupabaseClient();

  const governmentAgencies: GovernmentEntityResponse[] = await db.transaction(
    async (tx) => {
      return await tx
        .select({
          id: governmentAgencyTable.id,
          name: governmentAgencyTable.name,
          department: {
            name: governmentDepartmentTable.name,
          },
        })
        .from(governmentAgencyTable)
        .leftJoin(
          governmentDepartmentTable,
          eq(governmentDepartmentTable.id, governmentAgencyTable.departmentId)
        )
        .limit(50);
    }
  );

  if (!governmentAgencies) {
    logger.error('❌ GOVERNMENT AGENCIES NOT FOUND');
    return Response.json(
      { error: 'GOVERNMENT_AGENCIES_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(governmentAgencies);
});
