import type { GovernmentEntityResponse } from '@packages/database/types';
import { parseAsString } from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq, ilike } from '@packages/database/drizzle-orm';
import {
  governmentAgencyTable,
  governmentDepartmentTable,
} from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { name } = await ctx.params;
  const parsedName = parseAsString.parseServerSide(name);

  if (!parsedName) {
    logger.error('❌ MISSING GOVERNMENT AGENCY NAME');
    return Response.json(
      { error: 'MISSING_GOVERNMENT_AGENCY_NAME' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const governmentAgencies: GovernmentEntityResponse[] = await db.transaction(
    async (tx) => {
      return await tx
        .select({
          id: governmentAgencyTable.id,
          name: governmentAgencyTable.name,
          department: {
            name: governmentDepartmentTable.name,
          },
        })
        .from(governmentAgencyTable)
        .leftJoin(
          governmentDepartmentTable,
          eq(governmentDepartmentTable.id, governmentAgencyTable.departmentId)
        )
        .where(ilike(governmentAgencyTable.name, `%${parsedName}%`))
        .limit(50);
    }
  );

  if (!governmentAgencies) {
    logger.error('❌ GOVERNMENT AGENCIES NOT FOUND');
    return Response.json(
      { error: 'GOVERNMENT_AGENCIES_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(governmentAgencies);
});
