import type { GovernmentEntityResponse } from '@packages/database/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { aliasedTable, eq } from '@packages/database/drizzle-orm';
import {
  governmentAgencyTable,
  governmentDepartmentTable,
  governmentOfficeTable,
} from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, _ctx) => {
  const db = await createDrizzleSupabaseClient();

  const department = aliasedTable(governmentDepartmentTable, 'department');
  const agency = aliasedTable(governmentAgencyTable, 'agency');
  const office = aliasedTable(governmentOfficeTable, 'office');
  const agencyDepartment = aliasedTable(
    governmentDepartmentTable,
    'agency_department'
  );

  const governmentOffices: GovernmentEntityResponse[] = await db.transaction(
    async (tx) => {
      return await tx
        .select({
          id: office.id,
          name: office.name,
          department: {
            name: department.name,
          },
          agency: {
            name: agency.name,
            department: agencyDepartment.name,
          },
        })
        .from(office)
        .leftJoin(department, eq(department.id, office.departmentId))
        .leftJoin(agency, eq(agency.id, office.agencyId))
        .leftJoin(
          agencyDepartment,
          eq(agencyDepartment.id, agency.departmentId)
        )
        .limit(50);
    }
  );

  if (!governmentOffices) {
    logger.error('❌ GOVERNMENT OFFICES NOT FOUND');
    return Response.json(
      { error: 'GOVERNMENT_OFFICES_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(governmentOffices);
});
