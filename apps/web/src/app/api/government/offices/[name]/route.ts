import type { GovernmentEntityResponse } from '@packages/database/types';
import { parseAsString } from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { aliasedTable, eq, ilike } from '@packages/database/drizzle-orm';
import {
  governmentAgencyTable,
  governmentDepartmentTable,
  governmentOfficeTable,
} from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { name } = await ctx.params;
  const parsedName = parseAsString.parseServerSide(name);

  if (!parsedName) {
    logger.error('❌ MISSING GOVERNMENT OFFICE NAME');
    return Response.json(
      { error: 'MISSING_GOVERNMENT_OFFICE_NAME' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const department = aliasedTable(governmentDepartmentTable, 'department');
  const agency = aliasedTable(governmentAgencyTable, 'agency');
  const office = aliasedTable(governmentOfficeTable, 'office');
  const agencyDepartment = aliasedTable(
    governmentDepartmentTable,
    'agency_department'
  );

  const governmentOffices: GovernmentEntityResponse[] = await db.transaction(
    async (tx) => {
      return await tx
        .select({
          id: office.id,
          name: office.name,
          department: {
            name: department.name,
          },
          agency: {
            name: agency.name,
            department: agencyDepartment.name,
          },
        })
        .from(office)
        .leftJoin(department, eq(department.id, office.departmentId))
        .leftJoin(agency, eq(agency.id, office.agencyId))
        .leftJoin(
          agencyDepartment,
          eq(agencyDepartment.id, agency.departmentId)
        )
        .where(ilike(office.name, `%${parsedName}%`))
        .limit(50);
    }
  );

  if (!governmentOffices) {
    logger.error('❌ GOVERNMENT OFFICES NOT FOUND');
    return Response.json(
      { error: 'GOVERNMENT_OFFICES_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(governmentOffices);
});
