import type { ServiceResponse } from '@packages/database/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { serviceTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';

export const GET = withAuth(async (_req, _ctx) => {
  const db = await createDrizzleSupabaseClient();

  const services: ServiceResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select({ id: serviceTable.id, name: serviceTable.name })
      .from(serviceTable);
  });

  if (!services) {
    logger.error('❌ SERVICES NOT FOUND');
    return Response.json(
      { error: 'SERVICES_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(services);
});
