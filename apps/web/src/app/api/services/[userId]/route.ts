import type { UserServiceResponse } from '@packages/database/types';

import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  payRateTable,
  serviceTable,
  userServiceTable,
} from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const userServices: UserServiceResponse[] = await db.transaction(
    async (tx) => {
      return await tx
        .select({
          id: userServiceTable.id,
          expertiseLevel: userServiceTable.expertiseLevel,
          fixedPrice: userServiceTable.fixedPrice,
          payRate: {
            id: payRateTable.id,
            label: payRateTable.label,
          },
          service: {
            id: serviceTable.id,
            name: serviceTable.name,
          },
        })
        .from(userServiceTable)
        .innerJoin(
          serviceTable,
          eq(serviceTable.id, userServiceTable.serviceId)
        )
        .leftJoin(payRateTable, eq(payRateTable.id, userServiceTable.payRateId))
        .where(eq(userServiceTable.userId, parsedUserId));
    }
  );

  if (!userServices) {
    logger.error('❌ USER SERVICES NOT FOUND');
    return Response.json(
      { error: 'USER_SERVICES_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(userServices);
});
