import type { PositionResponse } from '@packages/database/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { positionTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, _ctx) => {
  const db = await createDrizzleSupabaseClient();

  const positions: PositionResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select({ id: positionTable.id, name: positionTable.name })
      .from(positionTable)
      .limit(20);
  });

  if (!positions) {
    logger.error('❌ POSITIONS NOT FOUND');
    return Response.json(
      { error: 'POSITIONS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(positions);
});
