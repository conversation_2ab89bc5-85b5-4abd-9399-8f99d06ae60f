import type { PositionResponse } from '@packages/database/types';
import { parseAsString } from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { ilike } from '@packages/database/drizzle-orm';
import { positionTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';

export const GET = withAuth(async (_req, ctx) => {
  const { name } = await ctx.params;
  const parsedName = parseAsString.parseServerSide(name);

  if (!parsedName) {
    logger.error('❌ MISSING POSITION NAME');
    return Response.json(
      { error: 'MISSING_POSITION_NAME' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const positions: PositionResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select({ id: positionTable.id, name: positionTable.name })
      .from(positionTable)
      .where(ilike(positionTable.name, `%${name}%`))
      .limit(20);
  });

  if (!positions) {
    logger.error('❌ POSITIONS NOT FOUND');
    return Response.json(
      { error: 'POSITIONS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(positions);
});
