import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { chatRoomMemberTable, usersTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';
import { parseAsUuid } from '@/utils/nuqs-parser';

export const GET = withAuth(async (_req, ctx) => {
  const { roomId } = await ctx.params;
  const parsedRoomId = parseAsUuid.parseServerSide(roomId);

  if (!parsedRoomId) {
    logger.error('❌ MISSING ROOM ID');
    return Response.json(
      { error: 'MISSING_ROOM_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const chatRoomMembers = await db.transaction(async (tx) => {
    return await tx
      .select({
        roomId: chatRoomMemberTable.roomId,
        hasRead: chatRoomMemberTable.hasRead,
        joinedAt: chatRoomMemberTable.joinedAt,
        isArchived: chatRoomMemberTable.isArchived,
        member: {
          id: usersTable.id,
          avatar: usersTable.avatar,
          firstName: usersTable.firstName,
          lastName: usersTable.lastName,
          username: usersTable.username,
        },
      })
      .from(chatRoomMemberTable)
      .innerJoin(usersTable, eq(usersTable.id, chatRoomMemberTable.userId))
      .where(eq(chatRoomMemberTable.roomId, parsedRoomId));
  });

  if (!chatRoomMembers) {
    logger.error('❌ CHAT ROOM MEMBERS NOT FOUND');
    return Response.json(
      { error: 'CHAT_ROOM_MEMBERS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(chatRoomMembers);
});
