import type { NextRequest } from 'next/server';

import { createSearchParamsCache, parseAsBoolean } from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { and, count, eq } from '@packages/database/drizzle-orm';
import { chatRoomMemberTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';
import { parseAsUuid } from '@/utils/nuqs-parser';

const searchParamsCache = createSearchParamsCache({
  isArchived: parseAsBoolean.withDefault(false),
});

export const GET = withAuth(async (req: NextRequest, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const searchParams = req.nextUrl.searchParams;
  const { isArchived } = searchParamsCache.parse(
    Object.fromEntries(searchParams.entries())
  );

  const db = await createDrizzleSupabaseClient();

  const result = await db.transaction(async (tx) => {
    const [countResult] = await tx
      .select({ count: count() })
      .from(chatRoomMemberTable)
      .where(
        and(
          eq(chatRoomMemberTable.userId, parsedUserId),
          eq(chatRoomMemberTable.hasRead, false),
          eq(chatRoomMemberTable.isArchived, isArchived)
        )
      );

    return countResult?.count ?? 0;
  });

  return Response.json(result);
});
