import type { NextRequest } from 'next/server';

import {
  createSearchParamsCache,
  parseAsBoolean,
  parseAsInteger,
  parseAsString,
} from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { getUserChatRooms } from '@packages/database/functions';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';

const searchParamsCache = createSearchParamsCache({
  withMessagesOnly: parseAsBoolean.withDefault(false),
  isArchived: parseAsBoolean.withDefault(false),
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  searchName: parseAsString.withDefault(''),
  sortByName: parseAsBoolean.withDefault(false),
});

export const GET = withAuth(async (req: NextRequest, ctx) => {
  const searchParams = req.nextUrl.searchParams;
  const parsedSearchParams = searchParamsCache.parse(
    Object.fromEntries(searchParams.entries())
  );

  const userId = ctx.authUser.id;
  const dbAdmin = await createDrizzleSupabaseClient({ admin: true });

  const chatRooms = await getUserChatRooms(dbAdmin, {
    userId,
    ...parsedSearchParams,
  });

  if (!chatRooms) {
    logger.error('❌ CHAT ROOMS NOT FOUND');
    return Response.json(
      { error: 'CHAT_ROOMS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(chatRooms);
});
