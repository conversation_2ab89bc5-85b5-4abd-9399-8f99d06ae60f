import { createDrizzleSupabaseClient } from '@packages/database';
import { getChatRoomIdForUsers } from '@packages/database/functions';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';
import { parseAsUuid } from '@/utils/nuqs-parser';

export const GET = withAuth(async (_req, ctx) => {
  const { targetUserId } = await ctx.params;
  const parsedTargetUserId = parseAsUuid.parseServerSide(targetUserId);

  if (!parsedTargetUserId) {
    logger.error('❌ MISSING TARGET USER ID');
    return Response.json(
      { error: 'MISSING_TARGET_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const userId = ctx.authUser.id;
  const dbAdmin = await createDrizzleSupabaseClient({ admin: true });

  const chatRoomId = await dbAdmin.transaction(async (tx) => {
    return await getChatRoomIdForUsers(tx, {
      user1Id: parsedTargetUserId,
      user2Id: userId,
    });
  });

  if (!chatRoomId) {
    logger.error('❌ CHAT ROOM ID NOT FOUND');
    return Response.json(
      { error: 'CHAT_ROOM_ID_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(chatRoomId);
});
