import { createDrizzleSupabaseClient } from '@packages/database';
import { and, eq } from '@packages/database/drizzle-orm';
import { chatRoomMemberTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';
import { parseAsUuid } from '@/utils/nuqs-parser';

export const GET = withAuth(async (_req, ctx) => {
  const { roomId } = await ctx.params;
  const parsedRoomId = parseAsUuid.parseServerSide(roomId);

  if (!parsedRoomId) {
    logger.error('❌ MISSING ROOM ID');
    return Response.json(
      { error: 'MISSING_ROOM_ID' },
      {
        status: 400,
      }
    );
  }

  const userId = ctx.authUser.id;
  const db = await createDrizzleSupabaseClient();

  const isArchived = await db.transaction(async (tx) => {
    const [chatRoomMember] = await tx
      .select({ isArchived: chatRoomMemberTable.isArchived })
      .from(chatRoomMemberTable)
      .where(
        and(
          eq(chatRoomMemberTable.roomId, parsedRoomId),
          eq(chatRoomMemberTable.userId, userId),
          eq(chatRoomMemberTable.isArchived, true)
        )
      )
      .limit(1);
    return chatRoomMember?.isArchived ?? false;
  });

  return Response.json(isArchived);
});
