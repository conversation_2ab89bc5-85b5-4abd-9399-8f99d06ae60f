import type { SubscriptionResponse } from '@packages/database/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  membershipTable,
  organizationSubscriptionTable,
  subscriptionTable,
} from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';

export const GET = withAuth(async (_req, ctx) => {
  const userId = ctx.authUser.id;

  const db = await createDrizzleSupabaseClient();

  const [subscription]: SubscriptionResponse[] = await db.transaction(
    async (tx) => {
      return await tx
        .select({
          id: subscriptionTable.id,
          status: subscriptionTable.status,
          currency: subscriptionTable.currency,
          interval: subscriptionTable.interval,
          cancelAtPeriodEnd: subscriptionTable.cancelAtPeriodEnd,
          intervalCount: subscriptionTable.intervalCount,
          priceId: subscriptionTable.priceId,
          quantity: subscriptionTable.quantity,
          plan: subscriptionTable.plan,
          createdAt: subscriptionTable.createdAt,
          cancelAt: subscriptionTable.cancelAt,
          periodStartsAt: subscriptionTable.periodStartsAt,
          periodEndsAt: subscriptionTable.periodEndsAt,
          trialStartsAt: subscriptionTable.trialStartsAt,
          trialEndsAt: subscriptionTable.trialEndsAt,
        })
        .from(subscriptionTable)
        .innerJoin(
          organizationSubscriptionTable,
          eq(organizationSubscriptionTable.subscriptionId, subscriptionTable.id)
        )
        .innerJoin(
          membershipTable,
          eq(
            organizationSubscriptionTable.organizationId,
            membershipTable.organizationId
          )
        )
        .where(eq(membershipTable.userId, userId))
        .limit(1);
    }
  );

  if (!subscription) {
    logger.error('❌ SUBSCRIPTION NOT FOUND');
    return Response.json({ error: 'SUBSCRIPTION_NOT_FOUND' }, { status: 404 });
  }

  return Response.json(subscription);
});
