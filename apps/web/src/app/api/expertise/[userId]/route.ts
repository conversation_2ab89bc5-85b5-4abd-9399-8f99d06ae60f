import type { UserSkillResponse } from '@packages/database/types';

import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { skillTable, userSkillTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const expertise: UserSkillResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select({
        id: userSkillTable.id,
        skill: {
          id: skillTable.id,
          name: skillTable.name,
        },
        expertiseLevel: userSkillTable.expertiseLevel,
      })
      .from(userSkillTable)
      .innerJoin(skillTable, eq(skillTable.id, userSkillTable.skillId))
      .where(eq(userSkillTable.userId, parsedUserId));
  });

  if (!expertise) {
    logger.error('❌ EXPERTISE NOT FOUND');
    return Response.json(
      { error: 'EXPERTISE_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(expertise);
});
