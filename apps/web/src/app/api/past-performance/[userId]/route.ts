import type { UserPastPerformanceResponse } from '@packages/database/types';

import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { aliasedTable, eq } from '@packages/database/drizzle-orm';
import {
  agencyContractTable,
  departmentContractTable,
  governmentAgencyTable,
  governmentContractTable,
  governmentDepartmentTable,
  governmentOfficeTable,
  governmentSubOfficeTable,
  officeContractTable,
  pastPerformanceTable,
  positionTable,
  subOfficeContractTable,
  userPastPerformanceTable,
} from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const userPastPerformances: UserPastPerformanceResponse[] =
    await db.transaction(async (tx) => {
      const upp = aliasedTable(userPastPerformanceTable, 'upp');
      const pp = aliasedTable(pastPerformanceTable, 'pp');
      const pos = aliasedTable(positionTable, 'pos');
      const gc = aliasedTable(governmentContractTable, 'gc');
      const dc = aliasedTable(departmentContractTable, 'dc');
      const gd = aliasedTable(governmentDepartmentTable, 'gd');
      const ac = aliasedTable(agencyContractTable, 'ac');
      const ga = aliasedTable(governmentAgencyTable, 'ga');
      const oc = aliasedTable(officeContractTable, 'oc');
      const go = aliasedTable(governmentOfficeTable, 'go');
      const soc = aliasedTable(subOfficeContractTable, 'soc');
      const gso = aliasedTable(governmentSubOfficeTable, 'gso');

      return await tx
        .select({
          pastPerformanceId: pp.id,
          awardDate: pp.awardDate,
          description: pp.description,
          position: { id: pos.id, name: pos.name },
          department: { id: gd.id, name: gd.name },
          agency: { id: ga.id, name: ga.name },
          office: { id: go.id, name: go.name },
          subOffice: { id: gso.id, name: gso.name },
          governmentContract: {
            id: gc.id,
            name: gc.name,
            value: gc.value,
            description: gc.description,
            rfpNumber: gc.rfpNumber,
          },
        })
        .from(upp)
        .innerJoin(pp, eq(upp.pastPerformanceId, pp.id))
        .innerJoin(pos, eq(pp.positionId, pos.id))
        .innerJoin(gc, eq(pp.governmentContractId, gc.id))
        .leftJoin(dc, eq(gc.id, dc.governmentContractId))
        .leftJoin(gd, eq(dc.departmentId, gd.id))
        .leftJoin(ac, eq(gc.id, ac.governmentContractId))
        .leftJoin(ga, eq(ac.agencyId, ga.id))
        .leftJoin(oc, eq(gc.id, oc.governmentContractId))
        .leftJoin(go, eq(oc.officeId, go.id))
        .leftJoin(soc, eq(gc.id, soc.governmentContractId))
        .leftJoin(gso, eq(soc.subOfficeId, gso.id))
        .where(eq(upp.userId, parsedUserId));
    });

  if (!userPastPerformances) {
    logger.error('❌ PAST PERFORMANCE NOT FOUND');
    return Response.json(
      { error: 'PAST_PERFORMANCE_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(userPastPerformances);
});
