import { createDrizzleSupabaseClient } from '@packages/database';
import { desc, eq } from '@packages/database/drizzle-orm';
import { userReviewRequestTable, usersTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';
import { parseAsUuid } from '@/utils/nuqs-parser';

export const GET = withAuth(async (_req, ctx) => {
  const { requesterId } = await ctx.params;
  const parsedRequesterId = parseAsUuid.parseServerSide(requesterId);

  if (!parsedRequesterId) {
    logger.error('❌ MISSING REQUESTER ID');
    return Response.json(
      { error: 'MISSING_REQUESTER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const userReviewRequests = await db.transaction(async (tx) => {
    return await tx
      .select({
        id: userReviewRequestTable.id,
        requester: {
          id: usersTable.id,
          avatar: usersTable.avatar,
          firstName: usersTable.firstName,
          lastName: usersTable.lastName,
          username: usersTable.username,
        },
        reviewerEmail: userReviewRequestTable.reviewerEmail,
        relationship: userReviewRequestTable.relationship,
        content: userReviewRequestTable.content,
        token: userReviewRequestTable.token,
        createdAt: userReviewRequestTable.createdAt,
        expirationDate: userReviewRequestTable.expirationDate,
      })
      .from(userReviewRequestTable)
      .innerJoin(
        usersTable,
        eq(userReviewRequestTable.requesterId, usersTable.id)
      )
      .where(eq(userReviewRequestTable.requesterId, parsedRequesterId))
      .orderBy(desc(userReviewRequestTable.createdAt));
  });

  if (!userReviewRequests) {
    logger.error('❌ USER REVIEW REQUESTS NOT FOUND');
    return Response.json(
      { error: 'USER_REVIEW_REQUESTS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(userReviewRequests);
});
