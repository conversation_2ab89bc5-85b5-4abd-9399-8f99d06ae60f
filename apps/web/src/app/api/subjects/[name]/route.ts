import type { SubjectResponse } from '@packages/database/types';
import { parseAsString } from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { ilike } from '@packages/database/drizzle-orm';
import { subjectTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { name } = await ctx.params;
  const parsedName = parseAsString.parseServerSide(name);

  if (!parsedName) {
    logger.error('❌ MISSING SUBJECT NAME');
    return Response.json(
      { error: 'MISSING_SUBJECT_NAME' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const subjects: SubjectResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select({ id: subjectTable.id, name: subjectTable.name })
      .from(subjectTable)
      .where(ilike(subjectTable.name, `%${name}%`))
      .limit(20);
  });

  if (!subjects) {
    logger.error('❌ SUBJECTS NOT FOUND');
    return Response.json(
      { error: 'SUBJECTS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(subjects);
});
