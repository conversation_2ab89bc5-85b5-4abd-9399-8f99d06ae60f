import type { SubjectResponse } from '@packages/database/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { subjectTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';

export const GET = withAuth(async (_req, _ctx) => {
  const db = await createDrizzleSupabaseClient();

  const subjects: SubjectResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select({ id: subjectTable.id, name: subjectTable.name })
      .from(subjectTable)
      .limit(20);
  });

  if (!subjects) {
    logger.error('❌ SUBJECTS NOT FOUND');
    return Response.json(
      { error: 'SUBJECTS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(subjects);
});
