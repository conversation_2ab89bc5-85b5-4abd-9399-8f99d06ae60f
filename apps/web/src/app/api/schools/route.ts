import type { SchoolResponseBasic } from '@packages/database/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { schoolTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, _ctx) => {
  const db = await createDrizzleSupabaseClient();

  const schools: SchoolResponseBasic[] = await db.transaction(async (tx) => {
    return await tx
      .select({ id: schoolTable.id, name: schoolTable.name })
      .from(schoolTable)
      .limit(20);
  });

  if (!schools) {
    logger.error('❌ SCHOOLS NOT FOUND');
    return Response.json(
      { error: 'SCHOOLS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(schools);
});
