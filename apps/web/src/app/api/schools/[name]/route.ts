import type { SchoolResponseBasic } from '@packages/database/types';
import { parseAsString } from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { ilike } from '@packages/database/drizzle-orm';
import { schoolTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { name } = await ctx.params;
  const parsedName = parseAsString.parseServerSide(name);

  if (!parsedName) {
    logger.error('❌ MISSING SCHOOL NAME');
    return Response.json(
      { error: 'MISSING_SCHOOL_NAME' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const schools: SchoolResponseBasic[] = await db.transaction(async (tx) => {
    return await tx
      .select({ id: schoolTable.id, name: schoolTable.name })
      .from(schoolTable)
      .where(ilike(schoolTable.name, `%${parsedName}%`))
      .limit(20);
  });

  if (!schools) {
    logger.error('❌ SCHOOLS NOT FOUND');
    return Response.json(
      { error: 'SCHOOLS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(schools);
});
