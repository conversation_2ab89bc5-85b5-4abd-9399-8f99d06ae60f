import type { MessageResponse } from '@packages/database/types';
import type { NextRequest } from 'next/server';

import { createSearchParamsCache, parseAsInteger } from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { desc, eq } from '@packages/database/drizzle-orm';
import { messageTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';
import { parseAsUuid } from '@/utils/nuqs-parser';

const searchParamsCache = createSearchParamsCache({
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  roomId: parseAsUuid.withDefault(''),
});

export const GET = withAuth(async (req: NextRequest, ctx) => {
  const searchParams = req.nextUrl.searchParams;
  const { roomId, page, perPage } = searchParamsCache.parse(
    Object.fromEntries(searchParams.entries())
  );

  if (!roomId) {
    logger.error('❌ MISSING ROOM ID');
    return Response.json(
      { error: 'MISSING_ROOM_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const messages: MessageResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select()
      .from(messageTable)
      .where(eq(messageTable.roomId, roomId))
      .orderBy(desc(messageTable.createdAt))
      .limit(perPage)
      .offset((page - 1) * perPage);
  });

  if (!messages) {
    logger.error('❌ MESSAGES NOT FOUND');
    return Response.json(
      { error: 'MESSAGES_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(messages);
});
