import type { NextRequest } from 'next/server';

import { createSearchParamsCache, parseAsInteger } from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { count, desc, eq } from '@packages/database/drizzle-orm';
import { connectionRequestTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';
import { parseAsUuid } from '@/utils/nuqs-parser';
import { connectionRequestSelect, receiver, requester } from '../utils';

const searchParamsCache = createSearchParamsCache({
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
});

export const GET = withAuth(async (req: NextRequest, ctx) => {
  const { receiverId } = await ctx.params;
  const parsedReceiverId = parseAsUuid.parseServerSide(receiverId);

  if (!parsedReceiverId) {
    logger.error('❌ MISSING RECEIVER ID');
    return Response.json(
      { error: 'MISSING_RECEIVER_ID' },
      {
        status: 400,
      }
    );
  }

  const searchParams = req.nextUrl.searchParams;
  const { page, perPage } = searchParamsCache.parse(
    Object.fromEntries(searchParams.entries())
  );

  const db = await createDrizzleSupabaseClient();

  const response = await db.transaction(async (tx) => {
    const [countResult] = await tx
      .select({ total: count() })
      .from(connectionRequestTable)
      .where(eq(connectionRequestTable.receiverId, parsedReceiverId));

    const { total = 0 } = countResult ?? {};

    if (total === 0) {
      return { requests: [], total };
    }

    const requests = await tx
      .select(connectionRequestSelect)
      .from(connectionRequestTable)
      .innerJoin(
        requester,
        eq(connectionRequestTable.requesterId, requester.id)
      )
      .innerJoin(receiver, eq(connectionRequestTable.receiverId, receiver.id))
      .where(eq(connectionRequestTable.receiverId, parsedReceiverId))
      .orderBy(desc(connectionRequestTable.createdAt))
      .limit(perPage)
      .offset((page - 1) * perPage);

    return { requests, total };
  });

  if (!response) {
    logger.error('❌ CONNECTION REQUESTS NOT FOUND');
    return Response.json(
      { error: 'CONNECTION_REQUESTS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(response);
});
