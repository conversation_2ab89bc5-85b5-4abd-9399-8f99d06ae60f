import type { ConnectionRequestResponse } from '@packages/database/types';

import { aliasedTable } from '@packages/database/drizzle-orm';
import { connectionRequestTable, usersTable } from '@packages/database/tables';

export const requester = aliasedTable(usersTable, 'requester');
export const receiver = aliasedTable(usersTable, 'receiver');

export const connectionRequestSelect = {
  id: connectionRequestTable.id,
  isRead: connectionRequestTable.isRead,
  requester: {
    id: requester.id,
    avatar: requester.avatar,
    firstName: requester.firstName,
    lastName: requester.lastName,
    username: requester.username,
    headline: requester.headline,
  },
  receiver: {
    id: receiver.id,
    avatar: receiver.avatar,
    firstName: receiver.firstName,
    lastName: receiver.lastName,
    username: receiver.username,
    headline: receiver.headline,
  },
  createdAt: connectionRequestTable.createdAt,
};
