import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { connectionRequestTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';

export const GET = withAuth(async (_req, ctx) => {
  const userId = ctx.authUser.id;
  const db = await createDrizzleSupabaseClient();

  const connectionRequests = await db.transaction(async (tx) => {
    return await tx
      .select({ isRead: connectionRequestTable.isRead })
      .from(connectionRequestTable)
      .where(eq(connectionRequestTable.receiverId, userId));
  });

  const read = connectionRequests.filter((req) => req.isRead).length;
  const unread = connectionRequests.filter((req) => !req.isRead).length;

  return Response.json({ read, unread });
});
