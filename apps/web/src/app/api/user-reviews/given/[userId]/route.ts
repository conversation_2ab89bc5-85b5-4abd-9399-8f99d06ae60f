import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { desc, eq } from '@packages/database/drizzle-orm';
import { userReviewTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withRateLimit } from '@/utils/api';
import { reviewer, user, userReviewSelect } from '../../utils';

export const GET = withRateLimit()(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const givenUserReviews = await db.transaction(async (tx) => {
    return await tx
      .select(userReviewSelect)
      .from(userReviewTable)
      .innerJoin(user, eq(userReviewTable.userId, user.id))
      .leftJoin(reviewer, eq(userReviewTable.reviewerId, reviewer.id))
      .where(eq(userReviewTable.reviewerId, parsedUserId))
      .orderBy(desc(userReviewTable.createdAt));
  });

  if (!givenUserReviews) {
    logger.error('❌ GIVEN USER REVIEWS NOT FOUND');
    return Response.json(
      { error: 'GIVEN_USER_REVIEWS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(givenUserReviews);
});
