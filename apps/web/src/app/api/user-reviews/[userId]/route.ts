import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { desc, eq } from '@packages/database/drizzle-orm';
import { userReviewTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withRateLimit } from '@/utils/api';
import { reviewer, user, userReviewSelect } from '../utils';

export const GET = withRateLimit()(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const dbAdmin = await createDrizzleSupabaseClient({ admin: true });

  const userReviews = await dbAdmin.transaction(async (tx) => {
    return await tx
      .select(userReviewSelect)
      .from(userReviewTable)
      .innerJoin(user, eq(userReviewTable.userId, user.id))
      .leftJoin(reviewer, eq(userReviewTable.reviewerId, reviewer.id))
      .where(eq(userReviewTable.userId, parsedUserId))
      .orderBy(desc(userReviewTable.createdAt));
  });

  if (!userReviews) {
    logger.error('❌ USER REVIEWS NOT FOUND');
    return Response.json(
      { error: 'USER_REVIEWS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(userReviews);
});
