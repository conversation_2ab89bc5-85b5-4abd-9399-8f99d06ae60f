import { aliasedTable } from '@packages/database/drizzle-orm';
import { userReviewTable, usersTable } from '@packages/database/tables';

export const user = aliasedTable(usersTable, 'user');
export const reviewer = aliasedTable(usersTable, 'reviewer');

export const userReviewSelect = {
  id: userReviewTable.id,
  user: {
    id: user.id,
    avatar: user.avatar,
    firstName: user.firstName,
    lastName: user.lastName,
    username: user.username,
  },
  reviewer: {
    id: reviewer.id,
    avatar: reviewer.avatar,
    firstName: reviewer.firstName,
    lastName: reviewer.lastName,
    username: reviewer.username,
  },
  reviewerEmail: userReviewTable.reviewerEmail,
  reviewerFirstName: userReviewTable.reviewerFirstName,
  reviewerLastName: userReviewTable.reviewerLastName,
  reviewerCompany: userReviewTable.reviewerCompany,
  reviewerLinkedin: userReviewTable.reviewerLinkedin,
  professionalismRating: userReviewTable.professionalismRating,
  communicationRating: userReviewTable.communicationRating,
  qualityOfWorkRating: userReviewTable.qualityOfWorkRating,
  timelinessRating: userReviewTable.timelinessRating,
  reliabilityRating: userReviewTable.reliabilityRating,
  overallRating: userReviewTable.overallRating,
  relationship: userReviewTable.relationship,
  content: userReviewTable.content,
  status: userReviewTable.status,
  createdAt: userReviewTable.createdAt,
};
