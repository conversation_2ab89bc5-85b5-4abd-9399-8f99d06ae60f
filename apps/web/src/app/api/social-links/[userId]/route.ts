import type { SocialLinkResponse } from '@packages/database/types';

import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { socialLinkTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const socialLinks: SocialLinkResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select({ id: socialLinkTable.id, url: socialLinkTable.url })
      .from(socialLinkTable)
      .where(eq(socialLinkTable.userId, parsedUserId));
  });

  if (!socialLinks) {
    logger.error('❌ SOCIAL LINKS NOT FOUND');
    return Response.json(
      { error: 'SOCIAL_LINKS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(socialLinks);
});
