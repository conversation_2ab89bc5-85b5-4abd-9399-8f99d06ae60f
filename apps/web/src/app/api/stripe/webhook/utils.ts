import type { SubscriptionRequest } from '@packages/database/types';
import type { Stripe } from 'stripe';

export function subscriptionMapper(
  subscription: Stripe.Subscription
): SubscriptionRequest {
  const lineItem = subscription.items.data[0];

  return {
    id: subscription.id,
    priceId: String(lineItem?.price?.id),
    quantity: Number(lineItem?.quantity),
    plan: String(subscription.metadata.plan),
    currency: subscription.currency,
    status: subscription.status ?? 'incomplete',
    interval: lineItem?.price?.recurring?.interval ?? null,
    intervalCount: lineItem?.price?.recurring?.interval_count ?? null,
    cancelAt: subscription.cancel_at ? toDate(subscription.cancel_at) : null,
    cancelAtPeriodEnd: subscription.cancel_at_period_end ?? false,
    createdAt: subscription.created ? toDate(subscription.created) : null,
    periodStartsAt: subscription.current_period_start
      ? toDate(subscription.current_period_start)
      : null,
    periodEndsAt: subscription.current_period_end
      ? toDate(subscription.current_period_end)
      : null,
    trialStartsAt: subscription.trial_start
      ? toDate(subscription.trial_start)
      : null,
    trialEndsAt: subscription.trial_end ? toDate(subscription.trial_end) : null,
  };
}

function toDate(timestamp: number) {
  return new Date(timestamp * 1000);
}
