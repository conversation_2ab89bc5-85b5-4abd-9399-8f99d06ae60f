import type { EducationResponse } from '@packages/database/types';

import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  degreeTable,
  educationTable,
  majorTable,
  minorTable,
  schoolTable,
  subjectTable,
  userDegreeTable,
} from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withRateLimit } from '@/utils/api';

export const GET = withRateLimit()(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient({ admin: true });

  const educations: EducationResponse[] = await db.transaction(async (tx) => {
    const educationData = await tx
      .select({
        id: educationTable.id,
        school: {
          id: schoolTable.id,
          name: schoolTable.name,
        },
        degree: {
          id: degreeTable.id,
          name: degreeTable.name,
        },
        userDegree: userDegreeTable,
      })
      .from(educationTable)
      .innerJoin(schoolTable, eq(educationTable.schoolId, schoolTable.id))
      .innerJoin(userDegreeTable, eq(educationTable.id, userDegreeTable.id))
      .innerJoin(degreeTable, eq(userDegreeTable.degreeId, degreeTable.id))
      .where(eq(educationTable.userId, parsedUserId));

    const educationsMap = new Map<number, EducationResponse>();

    for (const education of educationData) {
      if (!educationsMap.has(education.id)) {
        educationsMap.set(education.id, {
          id: education.id,
          school: education.school,
          degrees: [],
        });
      }

      const [majors, minors] = await Promise.all([
        tx
          .select({ id: subjectTable.id, name: subjectTable.name })
          .from(majorTable)
          .innerJoin(subjectTable, eq(majorTable.subjectId, subjectTable.id))
          .where(eq(majorTable.userDegreeId, education.userDegree.id)),
        tx
          .select({ id: subjectTable.id, name: subjectTable.name })
          .from(minorTable)
          .innerJoin(subjectTable, eq(minorTable.subjectId, subjectTable.id))
          .where(eq(minorTable.userDegreeId, education.userDegree.id)),
      ]);

      educationsMap.get(education.id)?.degrees.push({
        id: education.userDegree.id,
        degree: education.degree,
        startDate: education.userDegree.startDate,
        endDate: education.userDegree?.endDate ?? null,
        currentlyStudying: education.userDegree?.currentlyStudying ?? false,
        majors,
        minors,
      });
    }

    return Array.from(educationsMap.values());
  });

  if (!educations) {
    logger.error('❌ EDUCATIONS NOT FOUND');
    return Response.json(
      { error: 'EDUCATIONS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(educations);
});
