import { createDrizzleSupabaseClient } from '@packages/database';
import { getUserById } from '@packages/database/functions';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';

export const GET = withAuth(async (_req, ctx) => {
  const userId = ctx.authUser.id;
  const db = await createDrizzleSupabaseClient();

  const user = await getUserById(db, { userId });

  if (!user) {
    logger.error('❌ USER NOT FOUND');
    return Response.json(
      { error: 'USER_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(user);
});
