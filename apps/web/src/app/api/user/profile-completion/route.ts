import { createDrizzleSupabaseClient } from '@packages/database';
import { calculateProfileCompletion } from '@packages/database/functions';

import { withAuth } from '@/utils/api';

export const GET = withAuth(async (_req, ctx) => {
  const userId = ctx.authUser.id;

  const db = await createDrizzleSupabaseClient();
  const profileCompletion = await calculateProfileCompletion(db, {
    userId,
  });

  return Response.json(profileCompletion);
});
