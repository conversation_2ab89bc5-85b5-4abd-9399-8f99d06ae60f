import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { usersTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withRateLimit } from '@/utils/api';

export const GET = withRateLimit()(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const dbAdmin = await createDrizzleSupabaseClient({ admin: true });

  const [user] = await dbAdmin.transaction(async (tx) => {
    return await tx
      .select({
        id: usersTable.id,
        avatar: usersTable.avatar,
        username: usersTable.username,
        firstName: usersTable.firstName,
        createdAt: usersTable.createdAt,
      })
      .from(usersTable)
      .where(eq(usersTable.id, parsedUserId))
      .limit(1);
  });

  if (!user) {
    logger.error('❌ USER NOT FOUND');
    return Response.json(
      { error: 'USER_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(user);
});
