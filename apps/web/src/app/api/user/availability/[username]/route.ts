import { createDrizzleSupabaseClient } from '@packages/database';
import { and, eq, not, sql } from '@packages/database/drizzle-orm';
import { usersTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';
import { parseAsString } from 'nuqs/server';

import { withRateLimit } from '@/utils/api';
import { getRootPaths } from '@/utils/path';
import { getSupabaseServerClient } from '@packages/supabase/server';

export const GET = withRateLimit()(async (_req, ctx) => {
  const { username } = await ctx.params;
  const parsedUsername = parseAsString.parseServerSide(username);

  if (!parsedUsername) {
    logger.error('❌ MISSING USERNAME');
    return Response.json(
      { error: 'MISSING_USERNAME' },
      {
        status: 400,
      }
    );
  }

  const supabase = await getSupabaseServerClient();
  const { data: auth } = await supabase.auth.getUser();

  const userId = auth?.user?.id;
  const dbAdmin = await createDrizzleSupabaseClient({ admin: true });

  const isUsernameExistingPath = getRootPaths().some(
    (path) => path === username
  );

  if (isUsernameExistingPath || !username) {
    return Response.json(false);
  }

  const [userExists] = await dbAdmin.transaction(async (tx) => {
    return await tx
      .select({ exists: sql<number>`1` })
      .from(usersTable)
      .where(
        and(
          eq(usersTable.username, parsedUsername),
          userId ? not(eq(usersTable.id, userId)) : undefined
        )
      )
      .limit(1);
  });

  return Response.json(!userExists);
});
