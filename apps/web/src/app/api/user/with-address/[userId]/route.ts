import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { getUserWithAddressById } from '@packages/database/functions';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';

export const GET = withAuth(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const user = await getUserWithAddressById(db, { userId: parsedUserId });

  if (!user) {
    logger.error('❌ USER NOT FOUND');
    return Response.json(
      { error: 'USER_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(user);
});
