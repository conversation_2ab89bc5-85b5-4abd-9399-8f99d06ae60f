import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  addressTable,
  payRateTable,
  skillTable,
  userAddressTable,
  userSkillTable,
  usersTable,
} from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withAuth } from '@/utils/api';

export const GET = withAuth(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const user = await db.transaction(async (tx) => {
    const [user] = await tx
      .select({
        id: usersTable.id,
        avatar: usersTable.avatar,
        username: usersTable.username,
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        headline: usersTable.headline,
        biography: usersTable.biography,
        fteAvailable: usersTable.fteAvailable,
        isPublic: usersTable.isPublic,
        isAvailable: usersTable.isAvailable,
        unavailableUntil: usersTable.unavailableUntil,
        securityClearance: usersTable.securityClearance,
        legalStatus: usersTable.legalStatus,
        payRateLabel: payRateTable.label,
        createdAt: usersTable.createdAt,
        address: {
          id: addressTable.id,
          address1: addressTable.address1,
          address2: addressTable.address2,
          city: addressTable.city,
          state: addressTable.state,
          zipCode: addressTable.zipCode,
        },
      })
      .from(usersTable)
      .leftJoin(payRateTable, eq(payRateTable.id, usersTable.payRateId))
      .leftJoin(userAddressTable, eq(userAddressTable.userId, usersTable.id))
      .leftJoin(addressTable, eq(addressTable.id, userAddressTable.addressId))
      .where(eq(usersTable.id, parsedUserId))
      .limit(1);

    const expertise = await tx
      .select({
        id: userSkillTable.id,
        skillName: skillTable.name,
        expertiseLevel: userSkillTable.expertiseLevel,
      })
      .from(userSkillTable)
      .innerJoin(skillTable, eq(skillTable.id, userSkillTable.skillId))
      .where(eq(userSkillTable.userId, parsedUserId));

    return { ...user, expertise };
  });

  if (!user) {
    logger.error('❌ USER NOT FOUND');
    return Response.json(
      { error: 'USER_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(user);
});
