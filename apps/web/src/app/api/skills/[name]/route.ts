import type { SkillResponse } from '@packages/database/types';
import { parseAsString } from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { ilike } from '@packages/database/drizzle-orm';
import { skillTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { name } = await ctx.params;
  const parsedName = parseAsString.parseServerSide(name);

  if (!parsedName) {
    logger.error('❌ MISSING SKILL NAME');
    return Response.json(
      { error: 'MISSING_SKILL_NAME' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const skills: SkillResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select({ id: skillTable.id, name: skillTable.name })
      .from(skillTable)
      .where(ilike(skillTable.name, `%${name}%`))
      .limit(20);
  });

  if (!skills) {
    logger.error('❌ SKILLS NOT FOUND');
    return Response.json(
      { error: 'SKILLS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(skills);
});
