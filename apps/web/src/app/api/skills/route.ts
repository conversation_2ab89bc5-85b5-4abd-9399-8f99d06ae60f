import type { SkillResponse } from '@packages/database/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { skillTable } from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, _ctx) => {
  const db = await createDrizzleSupabaseClient();

  const skills: SkillResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select({ id: skillTable.id, name: skillTable.name })
      .from(skillTable)
      .limit(20);
  });

  if (!skills) {
    logger.error('❌ SKILLS NOT FOUND');
    return Response.json(
      { error: 'SKILLS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(skills);
});
