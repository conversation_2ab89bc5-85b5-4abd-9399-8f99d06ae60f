import type { UserCertificationResponse } from '@packages/database/types';

import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  certificationTable,
  userCertificationTable,
} from '@packages/database/tables';

import { withRateLimit } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withRateLimit()(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient({ admin: true });

  const userCertifications: UserCertificationResponse[] = await db.transaction(
    async (tx) => {
      return await tx
        .select({
          id: userCertificationTable.id,
          credentialId: userCertificationTable.credentialId,
          credentialUrl: userCertificationTable.credentialUrl,
          issuedDate: userCertificationTable.issuedDate,
          expirationDate: userCertificationTable.expirationDate,
          certificationName: certificationTable.name,
          certificationProvider: certificationTable.provider,
        })
        .from(userCertificationTable)
        .innerJoin(
          certificationTable,
          eq(certificationTable.id, userCertificationTable.certificationId)
        )
        .where(eq(userCertificationTable.userId, parsedUserId));
    }
  );

  if (!userCertifications) {
    logger.error('❌ USER CERTIFICATIONS NOT FOUND');
    return Response.json(
      { error: 'USER_CERTIFICATIONS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(userCertifications);
});
