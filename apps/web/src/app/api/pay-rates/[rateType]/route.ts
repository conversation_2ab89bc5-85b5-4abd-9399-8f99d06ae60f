import type { PayRateResponse } from '@packages/database/types';

import { parseAsStringEnum } from 'nuqs/server';

import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import { rateTypes } from '@packages/database/enums';
import { payRateTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withRateLimit } from '@/utils/api';

export const GET = withRateLimit()(async (_req, _ctx) => {
  const { rateType } = await _ctx.params;
  const parsedRateType = parseAsStringEnum([...rateTypes]).parseServerSide(
    rateType
  );

  if (!parsedRateType) {
    logger.error('❌ INVALID RATE TYPE');
    return Response.json({ error: 'INVALID_RATE_TYPE' }, { status: 400 });
  }

  const db = await createDrizzleSupabaseClient();

  const payRates: PayRateResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select({
        id: payRateTable.id,
        label: payRateTable.label,
        rateType: payRateTable.rateType,
        minValue: payRateTable.minValue,
        maxValue: payRateTable.maxValue,
      })
      .from(payRateTable)
      .where(eq(payRateTable.rateType, parsedRateType));
  });

  if (!payRates) {
    logger.error('❌ PAY RATES NOT FOUND');
    return Response.json(
      { error: 'PAY_RATES_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(payRates);
});
