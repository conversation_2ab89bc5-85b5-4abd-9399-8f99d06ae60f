import type { DegreeResponse } from '@packages/database/types';

import { createDrizzleSupabaseClient } from '@packages/database';
import { degreeTable } from '@packages/database/tables';
import { logger } from '@packages/utils/logger';

import { withRateLimit } from '@/utils/api';

export const GET = withRateLimit()(async (_req, _ctx) => {
  const db = await createDrizzleSupabaseClient();

  const degrees: DegreeResponse[] = await db.transaction(async (tx) => {
    return await tx
      .select({ id: degreeTable.id, name: degreeTable.name })
      .from(degreeTable);
  });

  if (!degrees) {
    logger.error('❌ DEGREES NOT FOUND');
    return Response.json(
      { error: 'DEGREES_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(degrees);
});
