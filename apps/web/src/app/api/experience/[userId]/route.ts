import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq, jsonAggBuildObject } from '@packages/database/drizzle-orm';
import {
  experiencePositionTable,
  experienceSkillTable,
  experienceTable,
  positionTable,
  skillTable,
} from '@packages/database/tables';

import { withRateLimit } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withRateLimit()(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient({ admin: true });

  const experiences = await db.transaction(async (tx) => {
    return await tx
      .select({
        id: experienceTable.id,
        locationType: experienceTable.locationType,
        employmentType: experienceTable.employmentType,
        startDate: experienceTable.startDate,
        endDate: experienceTable.endDate,
        description: experienceTable.description,
        city: experienceTable.city,
        state: experienceTable.state,
        isCurrentRole: experienceTable.isCurrentRole,
        companyName: experienceTable.companyName,
        position: {
          id: positionTable.id,
          name: positionTable.name,
        },
        skills: jsonAggBuildObject({
          id: skillTable.id,
          name: skillTable.name,
        }),
      })
      .from(experienceTable)
      .innerJoin(
        experiencePositionTable,
        eq(experiencePositionTable.experienceId, experienceTable.id)
      )
      .innerJoin(
        positionTable,
        eq(positionTable.id, experiencePositionTable.positionId)
      )
      .leftJoin(
        experienceSkillTable,
        eq(experienceSkillTable.experienceId, experienceTable.id)
      )
      .leftJoin(skillTable, eq(skillTable.id, experienceSkillTable.skillId))
      .where(eq(experienceTable.userId, parsedUserId))
      .groupBy(experienceTable.id, positionTable.id);
  });

  if (!experiences) {
    logger.error('❌ EXPERIENCES NOT FOUND');
    return Response.json(
      { error: 'EXPERIENCES_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(experiences);
});
