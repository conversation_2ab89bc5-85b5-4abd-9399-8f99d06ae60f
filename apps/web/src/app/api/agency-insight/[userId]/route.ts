import type { AgencyInsightResponse } from '@packages/database/types';

import { parseAsUuid } from '@/utils/nuqs-parser';
import { createDrizzleSupabaseClient } from '@packages/database';
import { eq } from '@packages/database/drizzle-orm';
import {
  agencyInsightTable,
  governmentAgencyTable,
  governmentDepartmentTable,
  governmentOfficeTable,
  governmentSubOfficeTable,
  positionTable,
} from '@packages/database/tables';

import { withAuth } from '@/utils/api';
import { logger } from '@packages/utils/logger';

export const GET = withAuth(async (_req, ctx) => {
  const { userId } = await ctx.params;
  const parsedUserId = parseAsUuid.parseServerSide(userId);

  if (!parsedUserId) {
    logger.error('❌ MISSING USER ID');
    return Response.json(
      { error: 'MISSING_USER_ID' },
      {
        status: 400,
      }
    );
  }

  const db = await createDrizzleSupabaseClient();

  const userAgencies: AgencyInsightResponse[] = await db.transaction(
    async (tx) => {
      return await tx
        .select({
          id: agencyInsightTable.id,
          description: agencyInsightTable.description,
          relationshipStrength: agencyInsightTable.relationshipStrength,
          position: {
            id: positionTable.id,
            name: positionTable.name,
          },
          department: {
            id: governmentDepartmentTable.id,
            name: governmentDepartmentTable.name,
          },
          agency: {
            id: governmentAgencyTable.id,
            name: governmentAgencyTable.name,
          },
          office: {
            id: governmentOfficeTable.id,
            name: governmentOfficeTable.name,
          },
          subOffice: {
            id: governmentSubOfficeTable.id,
            name: governmentSubOfficeTable.name,
          },
        })
        .from(agencyInsightTable)
        .innerJoin(
          positionTable,
          eq(positionTable.id, agencyInsightTable.positionId)
        )
        .innerJoin(
          governmentDepartmentTable,
          eq(governmentDepartmentTable.id, agencyInsightTable.departmentId)
        )
        .leftJoin(
          governmentAgencyTable,
          eq(governmentAgencyTable.id, agencyInsightTable.agencyId)
        )
        .leftJoin(
          governmentOfficeTable,
          eq(governmentOfficeTable.id, agencyInsightTable.officeId)
        )
        .leftJoin(
          governmentSubOfficeTable,
          eq(governmentSubOfficeTable.id, agencyInsightTable.subOfficeId)
        )
        .where(eq(agencyInsightTable.userId, parsedUserId));
    }
  );

  if (!userAgencies) {
    logger.error('❌ AGENCY INSIGHTS NOT FOUND');
    return Response.json(
      { error: 'AGENCY_INSIGHTS_NOT_FOUND' },
      {
        status: 404,
      }
    );
  }

  return Response.json(userAgencies);
});
