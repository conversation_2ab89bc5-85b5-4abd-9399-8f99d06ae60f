'use server';

// TODO: Delete this file. For testing purposes only.
import { z } from 'zod';

import { rateLimitedActionClient } from '@/core/server/actions/safe-action';
import { getNotificationsApi } from '@/lib/notifications/server/api';
import { NotificationType } from '@/lib/notifications/types';
import { createDrizzleSupabaseClient } from '@packages/database';
import { logger } from '@packages/utils/logger';

export const createDummyNotificationServerAction = rateLimitedActionClient
  .schema(
    z.object({
      userId: z.string().min(1, { message: 'User Id is required' }),
    })
  )
  .metadata({ name: 'send-contact-form-email' })
  .action(async ({ parsedInput }) => {
    const dbAdmin = await createDrizzleSupabaseClient({ admin: true });
    const notificationsApi = getNotificationsApi(dbAdmin);

    await notificationsApi.sendNotification({
      userId: parsedInput.userId,
      type: NotificationType.General,
      metadata: { messageId: '1', sender: 'Bob' },
      content: 'Hello, this is a test notification',
    });

    logger.info('Successfully created notification');
  });
