'use client';

import type React from 'react';
import { toast } from 'sonner';

import { createDummyNotificationServerAction } from '@/app/app/_test/action';
import { Button } from '@packages/ui/button';

import { useUserSession } from '@/core/hooks/user';

// TODO: Delete this file. For testing purposes only.

export const NotificationButton = () => {
  const userSession = useUserSession();

  const handleClick = async () => {
    try {
      const promise = createDummyNotificationServerAction({
        userId: userSession?.user?.id!,
      });
      toast.promise(promise, {
        loading: 'Creating notification...',
        success: 'Notification created!',
        error: 'Failed to create notification. Please try again.',
      });
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div>
      <Button className="mt-20" onClick={handleClick}>
        Create Notification
      </Button>
    </div>
  );
};
