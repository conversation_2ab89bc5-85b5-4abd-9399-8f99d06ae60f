import {
  DocumentTextIcon,
  LockClosedIcon,
  MapIcon,
  QuestionMarkCircleIcon,
} from '@heroicons/react/24/outline';
import { MembershipRole } from '@packages/database/enums';
import { useOnborda } from 'onborda';

import { useUserSession } from '@/core/hooks/user';

export interface HelpOption {
  Icon: React.ElementType;
  title: string;
  description: string;
  action?: VoidFunction;
  path?: string;
  iconClassName?: string;
}

export function useGetHelpOptions({ onSuccess }: { onSuccess?: VoidFunction }) {
  const { startOnborda } = useOnborda();
  const userSession = useUserSession();
  const { isClient, role: userRole } = userSession?.user!;

  const handleStartTour = () => {
    const isOwner = userRole === MembershipRole.Owner;
    const tourName = isClient
      ? isOwner
        ? 'client-owner-tour'
        : 'client-tour'
      : 'consultant-tour';

    startOnborda(tourName);
    if (onSuccess) {
      onSuccess();
    }
    return tourName;
  };

  const helpOptions: HelpOption[] = [
    {
      Icon: MapIcon,
      title: 'Take a Tour',
      description: "Take a guided tour of platform's key features",
      iconClassName: 'size-8',
      action: handleStartTour,
    },
    {
      Icon: LockClosedIcon,
      title: 'Privacy Policy',
      description: 'Read our privacy policy',
      path: '/legal/privacy',
    },
    {
      Icon: DocumentTextIcon,
      title: 'Terms of Service',
      description: 'Read our terms of service',
      path: '/legal/terms',
    },
    {
      Icon: QuestionMarkCircleIcon,
      title: 'FAQs',
      description: 'Find answers to common questions',
      path: '/pricing#pricing-faq-section',
    },
  ];

  return helpOptions;
}
